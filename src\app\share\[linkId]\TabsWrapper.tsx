'use client'

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from '@/components/ui/tabs'
import { ProfileContent } from './ProfileContent'
import { ProfileType } from '@/models/User'
import { useState, useEffect } from 'react'
import Image from 'next/image'
import { DEFAULT_AVATAR, DEFAULT_BANNER } from '@/lib/utils'
import { formatAddress } from '@/lib/utils'
import { TipButton } from '@/components/tip-button'

interface TabsWrapperProps {
  defaultTab: 'coin' | 'personal'
  hasCoinProfile: boolean
  hasPersonalProfile: boolean
  coinProfile?: ProfileType
  personalProfile?: ProfileType
  walletAddress?: string
  username?: string
}

export function TabsWrapper({
  defaultTab,
  hasCoinProfile,
  hasPersonalProfile,
  coinProfile,
  personalProfile,
  walletAddress,
  username
}: TabsWrapperProps) {
  const [activeTab, setActiveTab] = useState(defaultTab)
  
  // Keep track of the actual active profile to force re-renders
  const [avatarKey, setAvatarKey] = useState<string>(`${activeTab}-${Date.now()}`)
  const [bannerKey, setBannerKey] = useState<string>(`${activeTab}-${Date.now()}`)

  // Update keys when tab changes to force re-renders
  useEffect(() => {
    setAvatarKey(`${activeTab}-${Date.now()}`)
    setBannerKey(`${activeTab}-${Date.now()}`)
  }, [activeTab])

  // Get the profile data based on active tab
  const avatarSrc = activeTab === 'coin' 
    ? coinProfile?.avatar?.url || DEFAULT_AVATAR
    : personalProfile?.avatar?.url || DEFAULT_AVATAR
    
  const bannerSrc = activeTab === 'coin'
    ? coinProfile?.banner?.url || DEFAULT_BANNER
    : personalProfile?.banner?.url || DEFAULT_BANNER

  return (
    <>
      {/* Banner */}
      <div className="relative w-full h-48 bg-gray-200 border-b border-border">
        <Image
          src={bannerSrc}
          alt={`${activeTab === 'coin' ? 'Coin' : 'Personal'} Profile Banner`}
          fill
          className="object-cover"
          priority
          key={bannerKey}
        />
      </div>

      {/* Profile Info */}
      <div className="container mx-auto px-4 -mt-16">
        <div className="max-w-3xl mx-auto">
          <div className="flex items-start gap-6 mt-2">
            {/* Avatar */}
            <div className="relative w-32 h-32 rounded-full overflow-hidden border-4 border-background">
              <Image
                src={avatarSrc}
                alt={`${activeTab === 'coin' ? 'Coin' : 'Personal'} Profile Avatar`}
                fill
                className="object-cover"
                priority
                key={avatarKey}
              />
            </div>

            {/* User Info */}
            <div className="pt-16 flex-1">
              <div className="flex flex-col md:flex-row md:items-center justify-between gap-2">
                <div>
                  <h1 className="text-2xl font-bold">@{username}</h1>
                  <p className="text-muted-foreground">
                    {walletAddress && formatAddress(walletAddress)}
                  </p>
                </div>
                <div className="md:flex-shrink-0">
                  <TipButton 
                    recipientAddress={walletAddress}
                    username={username}
                    variant="primary"
                    size="lg"
                    className="border border-border px-3 md:px-4 text-sm md:text-base"
                  />
                </div>
              </div>
            </div>
          </div>

          <Tabs defaultValue={defaultTab} className="mt-8" onValueChange={(value) => setActiveTab(value as 'coin' | 'personal')}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="coin" disabled={!hasCoinProfile}>
                Coin Profile
              </TabsTrigger>
              <TabsTrigger value="personal" disabled={!hasPersonalProfile}>
                Creator Profile
              </TabsTrigger>
            </TabsList>

            <TabsContent value="coin">
              <ProfileContent 
                key={`coin-profile-${activeTab}`}
                profile={coinProfile} 
                walletAddress={walletAddress}
                username={username}
              />
            </TabsContent>

            <TabsContent value="personal">
              <ProfileContent 
                key={`personal-profile-${activeTab}`}
                profile={personalProfile} 
                walletAddress={walletAddress}
                username={username}
              />
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </>
  )
} 