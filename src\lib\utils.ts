import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import nacl from 'tweetnacl'
import bs58 from 'bs58'
import * as web3 from '@solana/web3.js'
import { Twitter, Instagram, Youtube, Facebook, Rocket, CandlestickChart, Send, Search, Radio, Link as LinkIcon, Linkedin, Globe, Github, Crosshair, Landmark, Briefcase, Palette } from 'lucide-react'

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

// Decrypt Phantom wallet response payload
export function decryptPhantomPayload(data: string, nonce: string, sharedSecret: Uint8Array) {
  try {
    const encryptedData = bs58.decode(data)
    const nonceBytes = bs58.decode(nonce)

    const decryptedData = nacl.box.open.after(encryptedData, nonceBytes, sharedSecret)

    if (!decryptedData) {
      throw new Error('Failed to decrypt payload')
    }

    return JSON.parse(Buffer.from(decryptedData).toString())
  } catch (error) {
    console.error('Error decrypting Phantom payload:', error)
    throw error
  }
}

export function formatAddress(address: string) {
  return `${address.slice(0, 6)}...${address.slice(-4)}`
}

export function isValidUrl(url: string) {
  try {
    new URL(url);
    return true;
  } catch (e) {
    return false;
  }
}

// Solana utility functions
const SOLANA_RPC_ENDPOINT = 'https://solana-mainnet.rpc.extrnode.com/34617682-9fc3-4bea-996e-f2fb340c5f8c';

// Ethereum RPC endpoint with API key
export const ETHEREUM_RPC_ENDPOINT = 'https://ethereum-mainnet.rpc.extrnode.com/03cc9198-9be5-45d0-9c80-d4dd4d9be811';

// Flag to use devnet for testing (set to true for testing, false for production)
const USE_DEVNET = false;

// Function to get Solana connection
const getSolanaConnection = async () => {
  const endpoint = USE_DEVNET 
    ? 'https://api.devnet.solana.com' 
    : SOLANA_RPC_ENDPOINT;
    
  try {
    // Use 'finalized' commitment level instead of 'confirmed' for stronger confirmation
    const connection = new web3.Connection(endpoint, 'finalized');
    await connection.getLatestBlockhash();
    return connection;
  } catch (error) {
    console.error(`Failed to connect to endpoint ${endpoint}:`, error);
    throw new Error('Failed to connect to Solana network');
  }
};

// Function to validate Solana address format
export function isValidSolanaAddress(address: string): boolean {
  try {
    if (!address) return false;
    
    // Preserve the exact case of the address
    const pubKey = new web3.PublicKey(address);
    
    // Verify the address matches EXACTLY (case-sensitive)
    return pubKey.toBase58() === address;
    
  } catch {
    return false;
  }
}

// Function to validate Ethereum address format
export function isValidEthereumAddress(address: string): boolean {
  try {
    if (!address) return false;
    return /^0x[a-fA-F0-9]{40}$/.test(address);
  } catch {
    return false;
  }
}

export async function sendSolanaTransaction(
  senderPublicKey: web3.PublicKey,
  recipientAddress: string,
  amount: string,
  phantom: any
): Promise<string> {
  // Validate recipient address with case sensitivity
  let recipientPublicKey: web3.PublicKey;
  try {
    recipientPublicKey = new web3.PublicKey(recipientAddress);
    
    // Critical: Verify the address matches exactly as provided
    if (recipientPublicKey.toBase58() !== recipientAddress) {
      throw new Error('Address case mismatch. Please use the exact address as provided.');
    }
  } catch (error) {
    throw new Error('Invalid Solana address or case mismatch');
  }

  const connection = await getSolanaConnection();
  
  try {
    const lamports = Math.floor(parseFloat(amount) * web3.LAMPORTS_PER_SOL);
    if (isNaN(lamports) || lamports <= 0) {
      throw new Error('Invalid amount');
    }
    
    const balance = await connection.getBalance(senderPublicKey);
    const minBalance = web3.LAMPORTS_PER_SOL / 1000; // 0.001 SOL for fees
    if (balance < lamports + minBalance) {
      throw new Error(`Insufficient balance. Need ${(lamports + minBalance) / web3.LAMPORTS_PER_SOL} SOL`);
    }

    const transaction = new web3.Transaction().add(
      web3.SystemProgram.transfer({
        fromPubkey: senderPublicKey,
        toPubkey: recipientPublicKey,
        lamports
      })
    );

    const { blockhash, lastValidBlockHeight } = await connection.getLatestBlockhash('finalized');
    transaction.recentBlockhash = blockhash;
    transaction.feePayer = senderPublicKey;

    const signed = await phantom.signTransaction(transaction);
    
    // Send with finalized commitment
    const signature = await connection.sendRawTransaction(signed.serialize(), {
      skipPreflight: false,
      preflightCommitment: 'finalized',
      maxRetries: 5
    });

    // Wait for finalization with a longer timeout
    const confirmation = await connection.confirmTransaction({
      signature,
      blockhash,
      lastValidBlockHeight
    }, 'finalized');

    if (confirmation.value.err) {
      throw new Error('Transaction failed to confirm');
    }

    // Double check the recipient's balance increased
    const finalRecipientBalance = await connection.getBalance(recipientPublicKey);
    if (finalRecipientBalance < lamports) {
      console.warn('Transaction confirmed but recipient balance not yet updated');
    }

    return signature;

  } catch (error: any) {
    console.error('Solana transaction error:', error);
    throw new Error(error.message || 'Failed to send transaction');
  }
}

export const DEFAULT_AVATAR = '/images/default-avatar.png'
export const DEFAULT_BANNER = '/images/default-banner.jpg'

export const PRESET_COIN_LINKS = [
  { title: 'Website', url: '', icon: 'globe' },
  { title: 'Whitepaper', url: '', icon: 'file-text' },
  { title: 'Token Contract', url: '', icon: 'file-code' },
  { title: 'Trading Chart', url: '', icon: 'line-chart' },
  { title: 'Community', url: '', icon: 'users' },
]

export const PRESET_CREATOR_LINKS = [
  { title: 'Portfolio', url: '', icon: 'briefcase' },
  { title: 'Social Media', url: '', icon: 'share2' },
  { title: 'NFT Collections', url: '', icon: 'image' },
  { title: 'Discord', url: '', icon: 'message-circle' },
  { title: 'Support', url: '', icon: 'heart' },
]

export const LINK_ICONS: Record<string, any> = {
  pumpfun: Rocket,
  chart: CandlestickChart,
  twitter: Twitter,
  telegram: Send,
  blockexplorer: Search,
  website: Globe,
  farcaster: Radio,
  instagram: Instagram,
  youtube: Youtube,
  facebook: Facebook,
  linkedin: Linkedin,
  github: Github,
  tradingbot: Crosshair,
  staking: Landmark,
  portfolio: Briefcase,
  nftcollection: Palette,
} 