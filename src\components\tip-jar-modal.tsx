'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON>alogHeader, <PERSON>alogT<PERSON>le, DialogFooter, DialogDescription } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Wallet, Loader2, Check, AlertCircle, Heart, ArrowRight } from 'lucide-react'
import { useAccount, useConnect, useDisconnect, useBalance, useSendTransaction, usePrepareSendTransaction, usePublicClient } from 'wagmi'
import { parseEther } from 'viem'
import toast from 'react-hot-toast'
import Image from 'next/image'
import { cn, sendSolanaTransaction, isValidSolanaAddress, isValidEthereumAddress } from '@/lib/utils'
import * as web3 from '@solana/web3.js'
import { Card } from '@/components/ui/card'

// Flag to indicate if we're using Solana devnet (should match the flag in utils.ts)
const USING_SOLANA_DEVNET = false;

interface TipJarModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  recipientAddress: string
  walletType?: string
  username: string
}

// Helper function to detect wallet type from address
function detectWalletType(address: string): 'ethereum' | 'solana' {
  // Solana addresses are 44 characters long and don't start with 0x
  if (address && address.length === 44 && !address.toLowerCase().startsWith('0x')) {
    return 'solana'
  } 
  // Ethereum addresses are 42 characters long (including 0x) and start with 0x
  else if (address && address.length === 42 && address.toLowerCase().startsWith('0x')) {
    return 'ethereum'
  }
  // Default to ethereum if we can't determine
  return 'ethereum'
}

export function TipJarModal({
  open,
  onOpenChange,
  recipientAddress,
  walletType,
  username
}: TipJarModalProps) {
  const [amount, setAmount] = useState('')
  const [status, setStatus] = useState<'idle' | 'connecting' | 'preparing' | 'sending' | 'success' | 'error'>('idle')
  const [errorMessage, setErrorMessage] = useState('')
  const [txHash, setTxHash] = useState('')
  const [showWalletSelect, setShowWalletSelect] = useState(false)
  
  const detectedType = detectWalletType(recipientAddress)
  const [networkType, setNetworkType] = useState(detectedType)

  // Only use wagmi hooks for Ethereum
  const { address, isConnected } = useAccount()
  const { connect, connectors } = useConnect()
  const { disconnect } = useDisconnect()
  const publicClient = usePublicClient()
  const { data: balance } = useBalance({
    address,
    enabled: isConnected && networkType === 'ethereum',
  })

  // Separate connection state for Solana
  const [solanaConnected, setSolanaConnected] = useState(false)
  const [solanaAddress, setSolanaAddress] = useState('')

  // Check Solana wallet connection on mount
  useEffect(() => {
    const checkPhantomConnection = async () => {
      if (window.phantom?.solana) {
        try {
          const response = await window.phantom.solana.connect({ onlyIfTrusted: true })
          setSolanaAddress(response.publicKey.toString())
          setSolanaConnected(true)
        } catch (error) {
          setSolanaConnected(false)
        }
      }
    }
    checkPhantomConnection()
  }, [])

  // Ethereum transaction hooks at component level
  const { config, error: prepareError } = usePrepareSendTransaction({
    to: recipientAddress,
    value: amount ? parseEther(amount) : undefined,
    enabled: isConnected && !!amount && networkType === 'ethereum',
  })
  
  const { sendTransaction, isLoading: isSending } = useSendTransaction({
    ...config,
    onMutate() {
      // Set status to sending when the transaction is about to be sent
      setStatus('sending')
      // Clear any previous transaction hash
      setTxHash('')
    },
    async onSuccess(data) {
      try {
        // Set the hash immediately
        setTxHash(data.hash)
        
        // Show pending transaction toast
        toast(() => (
          <div className="flex flex-col gap-1">
            <div>Transaction pending</div>
            <a 
              href={`https://etherscan.io/tx/${data.hash}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:underline text-sm"
            >
              View on Etherscan
            </a>
          </div>
        ), { duration: 10000 })

        // Wait for the transaction to be mined using the public client
        const receipt = await publicClient.waitForTransactionReceipt({ 
          hash: data.hash 
        })
        
        setStatus('success')
        toast.success('Tip sent successfully!')
        
        // Show the confirmed transaction toast
        toast(() => (
          <div className="flex flex-col gap-1">
            <div>Transaction confirmed!</div>
            <a 
              href={`https://etherscan.io/tx/${receipt.transactionHash}`}
              target="_blank"
              rel="noopener noreferrer"
              className="text-blue-500 hover:underline text-sm"
            >
              View on Etherscan
            </a>
          </div>
        ), { duration: 10000 })
      } catch (error: any) {
        console.error('Transaction failed:', error)
        setStatus('error')
        setErrorMessage('Transaction failed. Please check your wallet for details.')
        toast.error('Transaction failed')
      }
    },
    onError(error: any) {
      console.error('Transaction error:', error)
      setStatus('error')
      if (error.code === 'ACTION_REJECTED' || error.message?.includes('user rejected') || error.message?.includes('rejected')) {
        setErrorMessage('Transaction was rejected. Please try again.')
      } else {
        setErrorMessage(error.message || 'Failed to send transaction')
      }
      toast.error('Failed to send tip')
    },
  })

  // Add description ID for accessibility
  const modalDescriptionId = 'tip-jar-modal-description'

  // Group connectors by type
  const availableWallets = connectors.reduce((acc, connector) => {
    if (connector.id === 'metaMask') {
      acc.push({ id: 'metamask', name: 'MetaMask', connector, icon: '/images/metamask.svg' })
    } else if (connector.id === 'walletConnect') {
      acc.push({ id: 'walletconnect', name: 'WalletConnect', connector, icon: '/images/walletconnect.svg' })
    } else if (connector.id === 'phantom') {
      acc.push({ id: 'phantom', name: 'Phantom', connector, icon: '/images/phantom.svg' })
    }
    return acc
  }, [] as { id: string; name: string; connector: any; icon: string }[])

  const handleWalletSelect = async (selectedConnector: any) => {
    try {
      await connect({ connector: selectedConnector })
      setShowWalletSelect(false)
      setStatus('idle')
    } catch (error) {
      console.error('Failed to connect wallet:', error)
      setStatus('error')
      setErrorMessage('Failed to connect wallet. Please try again.')
    }
  }

  const handleConnectWallet = () => {
    setShowWalletSelect(true)
    setStatus('connecting')
  }

  // Handle Ethereum tips
  const sendEthereumTip = async () => {
    if (!address) {
      setStatus('error')
      setErrorMessage('Please connect your wallet first')
      return
    }

    if (!isValidEthereumAddress(recipientAddress)) {
      setStatus('error')
      setErrorMessage('Invalid Ethereum address')
      return
    }

    try {
      // First check if we can prepare the transaction
      if (!sendTransaction) {
        throw new Error('Transaction not ready')
      }

      // Check balance before proceeding
      if (balance) {
        const amountInWei = parseEther(amount)
        if (amountInWei > balance.value) {
          throw new Error('Insufficient balance for the transaction')
        }
      }

      // Set status to preparing before we send
      setStatus('preparing')
      
      // Send transaction - this will trigger MetaMask popup
      await sendTransaction()
      
      // If we get here, user has confirmed in MetaMask
      // The rest will be handled by onSuccess/onError callbacks
      
    } catch (error: any) {
      console.error('Ethereum transaction error:', error)
      setStatus('error')
      
      // Improved error messages
      if (error.message.includes('insufficient funds')) {
        setErrorMessage('Insufficient balance. Please make sure you have enough ETH to cover both the tip amount and gas fees.')
      } else if (error.code === 'ACTION_REJECTED' || error.message.includes('user rejected') || error.message.includes('rejected')) {
        setErrorMessage('Transaction was rejected. Please try again.')
      } else if (error.message.includes('transaction failed')) {
        setErrorMessage('Transaction failed. This could be due to network conditions or insufficient gas. Please try again.')
      } else {
        setErrorMessage(error.message || 'Failed to send transaction. Please check your wallet and try again.')
      }
      
      toast.error('Failed to send tip')
    }
  }

  // Handle Solana tips
  const sendSolanaTip = async () => {
    if (!window.phantom?.solana) {
      setStatus('error')
      setErrorMessage('Phantom wallet not installed')
      return
    }

    if (!isValidSolanaAddress(recipientAddress)) {
      setStatus('error')
      setErrorMessage('Invalid Solana address')
      return
    }

    try {
      setStatus('sending')
      
      const connection = await window.phantom.solana.connect()
      const senderPublicKey = new web3.PublicKey(connection.publicKey.toString())
      
      // Send transaction and get signature
      const signature = await sendSolanaTransaction(
        senderPublicKey,
        recipientAddress,
        amount,
        window.phantom.solana
      )
      
      setTxHash(signature)
      setStatus('success')
      toast.success('Tip sent successfully! It may take a moment to appear in your wallet.')
      
      // Add a link to view the transaction
      toast(() => (
        <div>
          <span>View transaction on </span>
          <a 
            href={`https://solscan.io/tx/${signature}`}
            target="_blank"
            rel="noopener noreferrer"
            className="text-blue-500 hover:underline"
          >
            Solscan
          </a>
        </div>
      ), { duration: 10000 })

    } catch (error: any) {
      console.error('Solana transaction error:', error)
      setStatus('error')
      setErrorMessage(error.message || 'Failed to send transaction')
      toast.error('Failed to send tip')
    }
  }

  const handleSendTip = async () => {
    if (!amount || parseFloat(amount) <= 0) {
      toast.error('Please enter a valid amount')
      return
    }

    if (networkType === 'ethereum') {
      if (!isConnected) {
        handleConnectWallet()
        return
      }

      // Check if using Phantom for Ethereum
      const isPhantomEthereum = window.phantom?.ethereum && address?.toLowerCase() === window.phantom.ethereum.selectedAddress?.toLowerCase()

      if (isPhantomEthereum) {
        // Handle Phantom Ethereum transaction directly
        try {
          setStatus('sending')
          
          // Convert amount to Wei and then to hex
          const amountInWei = parseEther(amount)
          const amountHex = `0x${amountInWei.toString(16)}`

          // Get current gas price
          const provider = window.phantom?.ethereum
          if (!provider) {
            throw new Error('Phantom provider not found')
          }

          const gasPrice = await provider.request({
            method: 'eth_gasPrice'
          })

          const nonce = await provider.request({
            method: 'eth_getTransactionCount',
            params: [address, 'latest']
          })

          // Prepare transaction with all parameters in hex format
          const transaction = {
            from: address,
            to: recipientAddress,
            value: amountHex,
            gas: '0x5208', // 21000 gas in hex
            gasPrice: gasPrice,
            nonce: nonce,
            chainId: '0x1' // Mainnet
          }

          const txHash = await provider.request({
            method: 'eth_sendTransaction',
            params: [transaction],
          })

          if (txHash) {
            setTxHash(txHash)
            toast(() => (
              <div className="flex flex-col gap-1">
                <div>Transaction pending</div>
                <a 
                  href={`https://etherscan.io/tx/${txHash}`}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-blue-500 hover:underline text-sm"
                >
                  View on Etherscan
                </a>
              </div>
            ), { duration: 10000 })

            // Wait for transaction confirmation
            const receipt = await provider.request({
              method: 'eth_getTransactionReceipt',
              params: [txHash],
            })

            setStatus('success')
            toast.success('Tip sent successfully!')
          }
        } catch (error: any) {
          console.error('Phantom Ethereum transaction error:', error)
          setStatus('error')
          if (error.code === 4001) {
            setErrorMessage('Transaction was rejected. Please try again.')
          } else {
            setErrorMessage(error.message || 'Failed to send transaction')
          }
          toast.error('Failed to send tip')
        }
      } else {
        // Handle regular Ethereum transaction through wagmi
        setStatus('preparing')
        if (prepareError) {
          setStatus('error')
          setErrorMessage(prepareError.message || 'Failed to prepare transaction')
          return
        }
        await sendEthereumTip()
      }
    } else {
      // Handle Solana connection and transaction
      if (!solanaConnected) {
        setStatus('connecting')
        if (!window.phantom?.solana) {
          setStatus('error')
          setErrorMessage('Phantom wallet not installed')
          return
        }
        try {
          const response = await window.phantom.solana.connect()
          setSolanaAddress(response.publicKey.toString())
          setSolanaConnected(true)
          setStatus('idle')
          return
        } catch (error: any) {
          setStatus('error')
          setErrorMessage(error.message || 'Failed to connect Phantom wallet')
          return
        }
      }
      await sendSolanaTip()
    }
  }

  const handleClose = () => {
    if (status !== 'sending') {
      setStatus('idle')
      setAmount('')
      setErrorMessage('')
      setTxHash('')
      onOpenChange(false)
    }
  }

  // Get the correct currency symbol and network name
  const getCurrencySymbol = () => networkType === 'ethereum' ? 'ETH' : 'SOL'
  const getNetworkName = () => networkType === 'ethereum' ? 'Ethereum' : 'Solana'
  const getExplorerUrl = () => {
    if (networkType === 'ethereum') {
      return 'https://etherscan.io/tx/'
    } else {
      // Use the correct explorer URL based on whether we're using devnet or mainnet
      return USING_SOLANA_DEVNET 
        ? 'https://explorer.solana.com/tx/?cluster=devnet' 
        : 'https://solscan.io/tx/'
    }
  }

  const getExplorerName = () => networkType === 'ethereum' ? 'Etherscan' : 'Solscan'

  // Update the isConnected check to work for both networks
  const isWalletConnected = networkType === 'ethereum' ? isConnected : solanaConnected

  const renderContent = () => {
    switch (status) {
      case 'idle':
        return (
          <>
            <DialogHeader>
              <DialogTitle className="text-center text-2xl font-bold">Tip @{username}</DialogTitle>
            </DialogHeader>
            <div className="space-y-6 py-6">
              <div className="flex justify-center mb-4">
                <div className={cn(
                  "w-20 h-20 bg-gradient-to-br rounded-full flex items-center justify-center shadow-lg",
                  networkType === 'ethereum' ? "from-blue-500 to-primary" : "from-purple-500 to-violet-600"
                )}>
                  <Heart className="w-10 h-10 text-white" />
                </div>
              </div>
              
              <div className="space-y-4">
                <Label htmlFor="amount" className="text-lg font-medium">
                  Amount ({getCurrencySymbol()})
                </Label>
                
                <div className="grid grid-cols-2 sm:grid-cols-4 gap-2">
                  {(networkType === 'ethereum' ? 
                    [
                      { value: '0.001', display: '0.001' },
                      { value: '0.005', display: '0.005' },
                      { value: '0.01', display: '0.01' },
                      { value: '0.05', display: '0.05' }
                    ] : 
                    [
                      { value: '0.01', display: '0.01' },
                      { value: '0.05', display: '0.05' },
                      { value: '0.1', display: '0.1' },
                      { value: '0.5', display: '0.5' }
                    ]
                  ).map(({ value, display }) => (
                    <Button 
                      key={value} 
                      variant={amount === value ? "default" : "outline"}
                      size="sm"
                      onClick={() => setAmount(value)}
                      className="w-full"
                    >
                      {display}
                    </Button>
                  ))}
                </div>
                
                <Input
                  id="amount"
                  type="number"
                  step="0.001"
                  min="0"
                  placeholder="Custom amount"
                  value={amount}
                  onChange={(e) => setAmount(e.target.value)}
                  className="text-lg h-12"
                />
              </div>
              
              {isConnected && networkType === 'ethereum' && balance && (
                <div className="bg-primary/5 p-3 rounded-lg">
                  <p className="text-sm text-center">
                    Your balance: <span className="font-bold">{balance.formatted} {balance.symbol}</span>
                  </p>
                </div>
              )}
              
              <div className="text-sm text-muted-foreground text-center space-y-1">
                <p>Recipient: <span className="font-mono">{recipientAddress.slice(0, 6)}...{recipientAddress.slice(-4)}</span></p>
                <p>Network: <span className="font-medium">{getNetworkName()}</span></p>
                {networkType === 'solana' && USING_SOLANA_DEVNET && (
                  <p className="text-xs mt-1 text-amber-500 font-bold">
                    DEVNET MODE: This is a test transaction on Solana Devnet
                  </p>
                )}
                {!isConnected && (
                  <p className="text-xs mt-1 text-amber-500">
                    You'll need to connect your wallet to send a tip
                  </p>
                )}
                {networkType === 'solana' && (
                  <p className="text-xs mt-1 text-amber-500">
                    You'll need Phantom wallet to send {getCurrencySymbol()} to this creator
                  </p>
                )}
                {networkType === 'ethereum' && (
                  <p className="text-xs mt-1 text-amber-500">
                    You'll need MetaMask or another Ethereum wallet to send {getCurrencySymbol()}
                  </p>
                )}
                {networkType === 'solana' && (
                  <p className="text-xs mt-1 text-amber-500">
                    Note: Solana addresses are case-sensitive. Please verify the exact address.
                  </p>
                )}
              </div>
            </div>
            <DialogFooter className="flex-col sm:flex-row gap-2">
              <Button variant="outline" onClick={handleClose} className="sm:flex-1">
                Cancel
              </Button>
              <Button 
                onClick={handleSendTip} 
                disabled={!amount}
                className={cn(
                  "sm:flex-1 bg-gradient-to-r",
                  networkType === 'ethereum' ? "from-blue-500 to-primary hover:from-blue-600 hover:to-primary/90" : "from-purple-500 to-violet-600 hover:from-purple-600 hover:to-violet-700"
                )}
              >
                {isConnected ? (
                  <>
                    Send Tip <ArrowRight className="w-4 h-4 ml-2" />
                  </>
                ) : (
                  <>
                    Connect Wallet <Wallet className="w-4 h-4 ml-2" />
                  </>
                )}
              </Button>
            </DialogFooter>
          </>
        )
      
      case 'connecting':
        return (
          <>
            <DialogHeader>
              <DialogTitle className="text-center">Connecting Wallet</DialogTitle>
            </DialogHeader>
            <div className="py-12 flex flex-col items-center justify-center space-y-4">
              <div className="relative">
                <div className={cn(
                  "w-16 h-16 rounded-full border-4 border-opacity-30 border-t-opacity-100 animate-spin",
                  networkType === 'ethereum' ? "border-blue-300 border-t-blue-500" : "border-purple-300 border-t-purple-500"
                )}></div>
                <Wallet className={cn(
                  "w-6 h-6 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
                  networkType === 'ethereum' ? "text-blue-500" : "text-purple-500"
                )} />
              </div>
              <p className="text-center text-lg font-medium mt-4">
                Please connect your {networkType === 'ethereum' ? 'Ethereum' : 'Phantom'} wallet
              </p>
              <p className="text-center text-sm text-muted-foreground">
                A connection request should appear in your wallet extension
              </p>
              {networkType === 'ethereum' && (
                <p className="text-center text-xs text-muted-foreground">
                  Compatible wallets: MetaMask, Phantom, or any other Ethereum wallet
                </p>
              )}
            </div>
          </>
        )
      
      case 'preparing':
        return (
          <>
            <DialogHeader>
              <DialogTitle className="text-center">Preparing Transaction</DialogTitle>
            </DialogHeader>
            <div className="py-12 flex flex-col items-center justify-center space-y-4">
              <div className="relative">
                <div className={cn(
                  "w-16 h-16 rounded-full border-4 border-opacity-30 border-t-opacity-100 animate-spin",
                  networkType === 'ethereum' ? "border-blue-300 border-t-blue-500" : "border-purple-300 border-t-purple-500"
                )}></div>
                <Heart className={cn(
                  "w-6 h-6 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
                  networkType === 'ethereum' ? "text-blue-500" : "text-purple-500"
                )} />
              </div>
              <p className="text-center text-lg font-medium mt-4">
                Preparing your transaction
              </p>
              <p className="text-center text-sm text-muted-foreground">
                This will only take a moment
              </p>
            </div>
          </>
        )
      
      case 'sending':
        return (
          <>
            <DialogHeader>
              <DialogTitle className="text-center">Sending Tip</DialogTitle>
            </DialogHeader>
            <div className="py-12 flex flex-col items-center justify-center space-y-4">
              <div className="relative">
                <div className={cn(
                  "w-16 h-16 rounded-full border-4 border-opacity-30 border-t-opacity-100 animate-spin",
                  networkType === 'ethereum' ? "border-blue-300 border-t-blue-500" : "border-purple-300 border-t-purple-500"
                )}></div>
                <Heart className={cn(
                  "w-6 h-6 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2",
                  networkType === 'ethereum' ? "text-blue-500" : "text-purple-500"
                )} />
              </div>
              <p className="text-center text-lg font-medium mt-4">
                {txHash ? 'Waiting for confirmation...' : 'Please confirm the transaction in your wallet'}
              </p>
              {txHash && (
                <>
                  <p className="text-center text-sm text-muted-foreground">
                    Transaction Hash: <span className="font-mono text-xs">{txHash.slice(0, 10)}...{txHash.slice(-8)}</span>
                  </p>
                  <a 
                    href={`${getExplorerUrl()}${txHash}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-primary hover:underline flex items-center"
                  >
                    View on {getExplorerName()} <ArrowRight className="w-3 h-3 ml-1" />
                  </a>
                </>
              )}
              <p className="text-center text-sm text-muted-foreground">
                This window will update once the transaction is confirmed
              </p>
            </div>
          </>
        )
      
      case 'success':
        return (
          <>
            <DialogHeader>
              <DialogTitle className="text-center text-2xl font-bold text-green-500">Thank You!</DialogTitle>
            </DialogHeader>
            <div className="py-8 flex flex-col items-center justify-center space-y-4">
              <div className="w-20 h-20 bg-gradient-to-br from-green-400 to-green-600 rounded-full flex items-center justify-center shadow-lg">
                <Check className="w-10 h-10 text-white" />
              </div>
              <p className="text-center text-lg font-medium mt-2">
                Your tip for @{username} is appreciated!
              </p>
              <p className="text-center text-sm text-muted-foreground">
                You've sent {amount} {getCurrencySymbol()}
              </p>
              {networkType === 'solana' && USING_SOLANA_DEVNET && (
                <p className="text-xs text-amber-500 font-bold">
                  NOTE: This was a test transaction on Solana Devnet (not real SOL)
                </p>
              )}
              {txHash && (
                <>
                  <p className="text-center text-sm text-muted-foreground">
                    Transaction Hash: <span className="font-mono text-xs">{txHash.slice(0, 10)}...{txHash.slice(-8)}</span>
                  </p>
                  <a 
                    href={`${getExplorerUrl()}${txHash}`}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-sm text-primary hover:underline flex items-center"
                  >
                    View on {getExplorerName()} <ArrowRight className="w-3 h-3 ml-1" />
                  </a>
                </>
              )}
            </div>
            <DialogFooter>
              <Button 
                onClick={handleClose}
                className="w-full bg-gradient-to-r from-green-500 to-green-600 hover:from-green-600 hover:to-green-700"
              >
                Close
              </Button>
            </DialogFooter>
          </>
        )
      
      case 'error':
        return (
          <>
            <DialogHeader>
              <DialogTitle className="text-center text-xl font-bold text-red-500">Transaction Failed</DialogTitle>
            </DialogHeader>
            <div className="py-8 flex flex-col items-center justify-center space-y-4">
              <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                <AlertCircle className="w-8 h-8 text-red-500" />
              </div>
              <p className="text-center text-base">
                {errorMessage || 'Something went wrong while processing your transaction.'}
              </p>
              {networkType === 'solana' && !window.phantom?.solana && (
                <a 
                  href="https://phantom.app/download" 
                  target="_blank" 
                  rel="noopener noreferrer"
                  className="text-sm text-primary hover:underline flex items-center mt-2"
                >
                  Download Phantom Wallet <ArrowRight className="w-3 h-3 ml-1" />
                </a>
              )}
              {networkType === 'ethereum' && errorMessage?.includes('gas') && (
                <p className="text-sm text-muted-foreground mt-2">
                  Make sure you have enough ETH to cover the transaction and gas fees.
                </p>
              )}
              {networkType === 'solana' && errorMessage?.includes('Insufficient balance') && (
                <p className="text-sm text-muted-foreground mt-2">
                  Please add more SOL to your wallet and try again.
                </p>
              )}
              {errorMessage?.includes('timed out') && (
                <p className="text-sm text-amber-500 mt-2">
                  Your transaction might still complete. Please check your wallet for updates.
                </p>
              )}
              {(errorMessage?.includes('RPC connection') || errorMessage?.includes('network')) && (
                <p className="text-sm text-muted-foreground mt-2">
                  The Solana network appears to be congested or experiencing issues. 
                  Try again later or try a different wallet.
                </p>
              )}
            </div>
            <DialogFooter className="flex-col sm:flex-row gap-2">
              <Button variant="outline" onClick={handleClose} className="sm:flex-1">Close</Button>
              <Button onClick={() => setStatus('idle')} className="sm:flex-1">Try Again</Button>
            </DialogFooter>
          </>
        )
    }
  }

  const renderWalletSelect = () => (
    <Dialog open={showWalletSelect} onOpenChange={(open) => {
      setShowWalletSelect(open)
      if (!open) setStatus('idle')
    }}>
      <DialogContent className="sm:max-w-[400px]">
        <DialogHeader>
          <DialogTitle>Connect Wallet</DialogTitle>
          <DialogDescription>
            Select a wallet to connect and send {networkType === 'ethereum' ? 'ETH' : 'SOL'}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          {availableWallets.map((wallet) => (
            <Card
              key={wallet.id}
              className={cn(
                "p-4 cursor-pointer hover:bg-accent transition-colors",
                "flex items-center justify-between"
              )}
              onClick={() => handleWalletSelect(wallet.connector)}
            >
              <div className="flex items-center gap-3">
                <div className="w-8 h-8">
                  <Image
                    src={wallet.icon}
                    alt={wallet.name}
                    width={32}
                    height={32}
                    priority
                  />
                </div>
                <span className="font-medium">{wallet.name}</span>
              </div>
              <ArrowRight className="w-5 h-5 text-muted-foreground" />
            </Card>
          ))}
        </div>
      </DialogContent>
    </Dialog>
  )

  return (
    <>
      <Dialog open={open} onOpenChange={handleClose}>
        <DialogContent 
          className="sm:max-w-[425px]"
          aria-describedby={modalDescriptionId}
        >
          <div id={modalDescriptionId} className="sr-only">
            Send a tip to {username} using {networkType === 'ethereum' ? 'Ethereum' : 'Solana'}
          </div>
          {renderContent()}
        </DialogContent>
      </Dialog>
      {renderWalletSelect()}
    </>
  )
} 