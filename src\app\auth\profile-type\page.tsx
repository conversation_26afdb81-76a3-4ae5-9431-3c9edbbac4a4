'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardHeader, CardTitle, CardDescription, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Loader2, Co<PERSON>, User } from 'lucide-react'
import toast from 'react-hot-toast'
import { useEffect, useState } from 'react'

export default function ProfileTypePage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [isMounted, setIsMounted] = useState(false)

  useEffect(() => {
    setIsMounted(true)
    
    // If user already has a profile type, redirect to dashboard
    if (session?.user?.profileType) {
      router.push('/dashboard')
    }

    // If not authenticated, redirect to home
    if (!session?.user?.walletAddress) {
      router.push('/')
    }
  }, [session, router])

  // Don't render anything during SSR
  if (!isMounted) {
    return null
  }

  const handleProfileTypeSelect = async (type: 'coin' | 'creator') => {
    try {
      const response = await fetch('/api/user/profile-type', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ profileType: type }),
      })

      if (!response.ok) {
        const data = await response.json()
        throw new Error(data.message || 'Failed to set profile type')
      }

      toast.success('Profile type set successfully')
      router.push('/dashboard')
    } catch (error: any) {
      console.error('Error setting profile type:', error)
      toast.error(error.message || 'Failed to set profile type')
    }
  }

  return (
    <main className="min-h-screen bg-gradient-to-b from-background to-background/80">
      <div className="container mx-auto px-4 py-16">
        <div className="max-w-3xl mx-auto space-y-8">
          <div className="text-center space-y-4">
            <h1 className="text-4xl font-bold text-gradient-primary">
              Choose Your Profile Type
            </h1>
            <p className="text-xl text-muted-foreground">
              Select the type of profile that best represents you
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card className="hover-card cursor-pointer" onClick={() => handleProfileTypeSelect('coin')}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Coin Profile</CardTitle>
                  <Coins className="h-6 w-6 text-primary" />
                </div>
                <CardDescription>
                  Perfect for cryptocurrency projects and tokens
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• PumpFun Integration</li>
                  <li>• DexScreener Charts</li>
                  <li>• Social Media Links</li>
                  <li>• Blockchain Explorer Links</li>
                  <li>• Community Channels</li>
                </ul>
              </CardContent>
            </Card>

            <Card className="hover-card cursor-pointer" onClick={() => handleProfileTypeSelect('creator')}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle>Creator Profile</CardTitle>
                  <User className="h-6 w-6 text-primary" />
                </div>
                <CardDescription>
                  Ideal for content creators and influencers
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-muted-foreground">
                  <li>• Social Media Integration</li>
                  <li>• Content Platform Links</li>
                  <li>• Portfolio Showcase</li>
                  <li>• Community Channels</li>
                  <li>• Personal Branding</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </main>
  )
} 