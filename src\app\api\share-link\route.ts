import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import connectDB from '@/lib/mongodb/mongoose'
import { User, UserType } from '@/models/User'
import { v4 as uuidv4 } from 'uuid'

interface ShareLink {
  id: string;
  createdAt: Date;
  expiresAt: Date | null;
}

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { expiresAt } = await req.json()

    await connectDB()
    const user = await User.findOne({ walletAddress: session.user.walletAddress })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Generate a new share link
    const shareLink: ShareLink = {
      id: uuidv4(),
      createdAt: new Date(),
      expiresAt: expiresAt ? new Date(expiresAt) : null
    }

    // Add the share link to the user's shareLinks array
    if (!user.shareLinks) {
      user.shareLinks = []
    }
    
    // Use updateOne to avoid validation issues with the entire document
    const result = await User.updateOne(
      { _id: user._id },
      { $push: { shareLinks: shareLink } }
    )

    if (result.modifiedCount === 0) {
      return NextResponse.json({ error: 'Failed to create share link' }, { status: 500 })
    }

    return NextResponse.json(shareLink)
  } catch (error: any) {
    console.error('Error generating share link:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}

export async function GET(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    await connectDB()
    const user = await User.findOne({ walletAddress: session.user.walletAddress })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Filter out expired links
    const activeLinks = user.shareLinks?.filter((link: ShareLink) => 
      !link.expiresAt || new Date(link.expiresAt) > new Date()
    ) || []

    return NextResponse.json(activeLinks)
  } catch (error: any) {
    console.error('Error fetching share links:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}

export async function DELETE(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { linkId } = await req.json()
    if (!linkId) {
      return NextResponse.json({ error: 'Link ID is required' }, { status: 400 })
    }

    await connectDB()
    const user = await User.findOne({ walletAddress: session.user.walletAddress })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Remove the specified share link
    const originalLength = user.shareLinks?.length || 0
    user.shareLinks = user.shareLinks?.filter((link: ShareLink) => link.id !== linkId) || []
    
    if (user.shareLinks.length === originalLength) {
      return NextResponse.json({ error: 'Share link not found' }, { status: 404 })
    }

    await user.save()
    return NextResponse.json({ message: 'Share link deleted successfully' })
  } catch (error: any) {
    console.error('Error deleting share link:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
} 