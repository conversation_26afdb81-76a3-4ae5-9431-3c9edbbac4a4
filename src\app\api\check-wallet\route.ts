import { NextResponse } from 'next/server'
import connectDB from '@/lib/mongodb/mongoose'
import { User } from '@/models/User'
import { isValidSolanaAddress } from '@/lib/utils'

export async function POST(req: Request) {
  try {
    const { username, walletAddress, walletType } = await req.json()

    if (!username || !walletAddress) {
      return NextResponse.json(
        { message: 'Username and wallet address are required' },
        { status: 400 }
      )
    }

    await connectDB()

    // Find user by username
    const user = await User.findOne({ 
      username: username.toLowerCase()
    })

    if (!user) {
      return NextResponse.json({
        isOwner: false,
        message: 'User not found'
      })
    }

    // Check if this wallet owns this username
    // For Solana addresses, use exact case match
    const isOwner = user.walletType === 'solana'
      ? user.walletAddress === walletAddress
      : user.walletAddress.toLowerCase() === walletAddress.toLowerCase()

    return NextResponse.json({
      isOwner,
      message: isOwner ? 'Wallet verified' : 'Username belongs to different wallet'
    })
  } catch (error) {
    console.error('Error checking wallet:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
} 