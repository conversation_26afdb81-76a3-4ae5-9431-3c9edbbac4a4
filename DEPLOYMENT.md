# Deployment Guide for CoinLink

This guide explains how to deploy the CoinLink application to Vercel using Docker.

## Prerequisites

- A [Vercel](https://vercel.com) account
- [Git](https://git-scm.com/) installed on your local machine
- [Node.js](https://nodejs.org/) (v18 or later) installed on your local machine

## Environment Variables

Before deploying, make sure to set up the following environment variables in your Vercel project:

- `MONGODB_URI`: Your MongoDB connection string
- `NEXTAUTH_SECRET`: A secure random string for NextAuth.js
- `NEXTAUTH_URL`: The URL of your deployed application
- `NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID`: Your WalletConnect project ID
- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key
- `SUPABASE_SERVICE_ROLE_KEY`: Your Supabase service role key

## Deployment Steps

### 1. Push your code to a Git repository

```bash
git init
git add .
git commit -m "Initial commit"
git remote add origin <your-repository-url>
git push -u origin main
```

### 2. Import your project to Vercel

1. Log in to your Vercel account
2. Click "Add New" > "Project"
3. Import your Git repository
4. Configure your project:
   - Framework Preset: Next.js
   - Build Command: Leave as default
   - Output Directory: Leave as default
   - Install Command: Leave as default

### 3. Configure Environment Variables

1. In your project settings, go to the "Environment Variables" tab
2. Add all the required environment variables listed above

### 4. Deploy

1. Click "Deploy"
2. Wait for the build to complete

## Vercel CLI Deployment (Alternative)

You can also deploy using the Vercel CLI:

1. Install the Vercel CLI:
   ```bash
   npm install -g vercel
   ```

2. Log in to Vercel:
   ```bash
   vercel login
   ```

3. Deploy:
   ```bash
   vercel
   ```

4. Follow the prompts to configure your project

## Docker Local Testing

To test the Docker build locally before deploying:

```bash
# Build the Docker image
docker build -t wallet-tree .

# Run the Docker container
docker run -p 3000:3000 wallet-tree
```

Visit `http://localhost:3000` to see your application running in Docker.

## Troubleshooting

- If you encounter issues with the Docker build, check the Vercel build logs for details
- Make sure all environment variables are correctly set
- Ensure your MongoDB instance is accessible from Vercel's servers
- Check that your Supabase project has the correct permissions set up 