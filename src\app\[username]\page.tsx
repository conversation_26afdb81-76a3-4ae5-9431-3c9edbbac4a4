import { User } from '@/models/User'
import connectDB from '@/lib/mongodb/mongoose'
import { notFound } from 'next/navigation'
import { TabsWrapper } from './TabsWrapper'
import { Footer } from "@/components/footer"

export const dynamic = 'force-dynamic'
export const revalidate = 0

interface ProfilePageProps {
  params: { username: string }
}

export async function generateMetadata({ params }: ProfilePageProps) {
  await connectDB()
  const user = await User.findOne({ username: params.username.toLowerCase() }).lean()

  if (!user) {
    return {
      title: 'Profile Not Found - CoinLink',
    }
  }

  return {
    title: `${user.username} - CoinLink`,
    description: user.coinProfile?.biography || user.personalProfile?.biography || 'View my crypto profile on CoinLink',
  }
}

export default async function ProfilePage({ params }: ProfilePageProps) {
  await connectDB()
  // Use .lean() to get a plain JavaScript object instead of a Mongoose document
  const user = await User.findOne({ username: params.username.toLowerCase() }).lean()

  if (!user) {
    notFound()
  }

  // Check if the profile is public
  if (!user.isPublic) {
    notFound()
  }

  // Create a clean user object with only the needed properties
  const cleanUser = {
    username: user.username,
    walletAddress: user.walletAddress,
    coinProfile: user.coinProfile ? {
      ...user.coinProfile,
      type: 'coin' as const,
      avatar: user.coinProfile.avatar,
      banner: user.coinProfile.banner,
    } : null,
    personalProfile: user.personalProfile ? {
      ...user.personalProfile,
      type: 'personal' as const,
      avatar: user.personalProfile.avatar,
      banner: user.personalProfile.banner,
    } : null,
  }

  const { coinProfile, personalProfile } = cleanUser
  const hasCoinProfile = coinProfile && (coinProfile.biography || coinProfile.contractAddress || Object.keys(coinProfile.links || {}).length > 0)
  const hasPersonalProfile = personalProfile && (personalProfile.biography || Object.keys(personalProfile.links || {}).length > 0)
  const defaultTab = hasCoinProfile ? 'coin' : 'personal'

  return (
    <div>
      <main className="min-h-screen bg-gradient-to-b from-background to-background/80">
        <TabsWrapper 
          defaultTab={defaultTab}
          hasCoinProfile={hasCoinProfile}
          hasPersonalProfile={hasPersonalProfile}
          coinProfile={coinProfile}
          personalProfile={personalProfile}
          walletAddress={cleanUser.walletAddress}
          username={cleanUser.username}
        />
      </main>
      <Footer />
    </div>
  )
} 