import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import connectDB from '@/lib/mongodb/mongoose'
import { User } from '@/models/User'

const DEFAULT_PROFILE = {
  biography: '',
  contractAddress: '',
  links: {},
  avatar: {
    url: '/images/default-avatar.svg',
    isDefault: true,
    metadata: null
  },
  banner: {
    url: '/images/default-banner.svg',
    isDefault: true,
    metadata: null
  }
}

export async function GET() {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    await connectDB()
    const user = await User.findOne({ walletAddress: session.user.walletAddress })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    // Ensure proper data structure for both profiles
    const response = {
      username: user.username,
      walletAddress: user.walletAddress,
      isPublic: user.isPublic || false,
      coinProfile: {
        biography: user.coinProfile?.biography || DEFAULT_PROFILE.biography,
        contractAddress: user.coinProfile?.contractAddress || DEFAULT_PROFILE.contractAddress,
        links: user.coinProfile?.links || DEFAULT_PROFILE.links,
        avatar: {
          url: user.coinProfile?.avatar?.url || DEFAULT_PROFILE.avatar.url,
          isDefault: user.coinProfile?.avatar?.isDefault ?? DEFAULT_PROFILE.avatar.isDefault,
          metadata: user.coinProfile?.avatar?.metadata || DEFAULT_PROFILE.avatar.metadata
        },
        banner: {
          url: user.coinProfile?.banner?.url || DEFAULT_PROFILE.banner.url,
          isDefault: user.coinProfile?.banner?.isDefault ?? DEFAULT_PROFILE.banner.isDefault,
          metadata: user.coinProfile?.banner?.metadata || DEFAULT_PROFILE.banner.metadata
        }
      },
      personalProfile: {
        biography: user.personalProfile?.biography || DEFAULT_PROFILE.biography,
        contractAddress: user.personalProfile?.contractAddress || DEFAULT_PROFILE.contractAddress,
        links: user.personalProfile?.links || DEFAULT_PROFILE.links,
        avatar: {
          url: user.personalProfile?.avatar?.url || DEFAULT_PROFILE.avatar.url,
          isDefault: user.personalProfile?.avatar?.isDefault ?? DEFAULT_PROFILE.avatar.isDefault,
          metadata: user.personalProfile?.avatar?.metadata || DEFAULT_PROFILE.avatar.metadata
        },
        banner: {
          url: user.personalProfile?.banner?.url || DEFAULT_PROFILE.banner.url,
          isDefault: user.personalProfile?.banner?.isDefault ?? DEFAULT_PROFILE.banner.isDefault,
          metadata: user.personalProfile?.banner?.metadata || DEFAULT_PROFILE.banner.metadata
        }
      }
    }

    return NextResponse.json(response)
  } catch (error: any) {
    console.error('Error fetching user profile:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
}

export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    await connectDB()
    const user = await User.findOne({ walletAddress: session.user.walletAddress })

    if (!user) {
      return NextResponse.json({ error: 'User not found' }, { status: 404 })
    }

    const body = await req.json()
    const { coinProfile, personalProfile, isPublic } = body

    console.log('PUT request body:', JSON.stringify(body, null, 2))

    // Handle isPublic field update
    if (typeof isPublic === 'boolean') {
      user.isPublic = isPublic
    }

    // Update coin profile if provided
    if (coinProfile) {
      user.coinProfile = {
        ...user.coinProfile || {},
        ...coinProfile,
        avatar: coinProfile.avatar ? {
          ...(user.coinProfile?.avatar || {}),
          ...coinProfile.avatar
        } : user.coinProfile?.avatar,
        banner: coinProfile.banner ? {
          ...(user.coinProfile?.banner || {}),
          ...coinProfile.banner
        } : user.coinProfile?.banner
      }
    }

    // Update personal profile if provided
    if (personalProfile) {
      user.personalProfile = {
        ...user.personalProfile || {},
        ...personalProfile,
        avatar: personalProfile.avatar ? {
          ...(user.personalProfile?.avatar || {}),
          ...personalProfile.avatar
        } : user.personalProfile?.avatar,
        banner: personalProfile.banner ? {
          ...(user.personalProfile?.banner || {}),
          ...personalProfile.banner
        } : user.personalProfile?.banner
      }
    }

    await user.save()

    console.log('User saved successfully. coinProfile.contractAddress:', user.coinProfile?.contractAddress)

    return NextResponse.json({
      username: user.username,
      walletAddress: user.walletAddress,
      isPublic: user.isPublic,
      coinProfile: user.coinProfile,
      personalProfile: user.personalProfile
    })
  } catch (error: any) {
    console.error('Error updating user profile:', error)
    return NextResponse.json({ error: error.message }, { status: 500 })
  }
} 