'use client'

import { <PERSON><PERSON><PERSON>ider } from "next-auth/react"
import { Web3Provider } from "./web3-provider"
import { Toaster } from "react-hot-toast"
import { useEffect } from "react"
import nacl from 'tweetnacl'
import bs58 from 'bs58'

export function Providers({ children }: { children: React.ReactNode }) {
  // Global Phantom mobile callback handler
  useEffect(() => {
    // Only run on client
    if (typeof window === 'undefined') return;
    // Check for phantom callback in URL
    const searchParams = new URLSearchParams(window.location.search);
    const phantomEncryptionPublicKey = searchParams.get('phantom_encryption_public_key');
    const nonce = searchParams.get('nonce');
    const data = searchParams.get('data');
    const errorCode = searchParams.get('errorCode');
    const errorMessage = searchParams.get('errorMessage');

    if (errorCode || errorMessage) {
      // Optionally show a toast here if you want
      window.history.replaceState({}, document.title, window.location.pathname);
      return;
    }

    if (phantomEncryptionPublicKey && nonce && data) {
      try {
        // Get stored keypair
        const storedKeyPair = localStorage.getItem('dappKeyPair');
        if (!storedKeyPair) throw new Error('No stored keypair found');
        const keypair = JSON.parse(storedKeyPair);
        const secretKey = bs58.decode(keypair.secretKey);
        // Create shared secret
        const sharedSecret = nacl.box.before(
          bs58.decode(phantomEncryptionPublicKey),
          secretKey
        );
        // Decrypt the data
        const decryptedData = nacl.box.open.after(
          bs58.decode(data),
          bs58.decode(nonce),
          sharedSecret
        );
        if (!decryptedData) throw new Error('Unable to decrypt data');
        const payload = JSON.parse(Buffer.from(decryptedData).toString('utf8'));
        if (payload.public_key) {
          // Dispatch a custom event so the login modal or any listener can handle login
          window.dispatchEvent(new CustomEvent('phantom-wallet-callback', { detail: payload }));
        }
        window.history.replaceState({}, document.title, window.location.pathname);
      } catch (e) {
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    }
  }, []);

  return (
    <SessionProvider>
      <Web3Provider>
        {children}
        <Toaster />
      </Web3Provider>
    </SessionProvider>
  )
} 