import { Chain, Connector, ConnectorData } from 'wagmi'
import { WindowProvider } from 'wagmi/window'
import * as web3 from '@solana/web3.js'

declare global {
  interface Window {
    phantom?: {
      solana?: {
        connect(): Promise<{ publicKey: { toString(): string } }>
        disconnect(): Promise<void>
        signMessage(message: Uint8Array): Promise<{ signature: Uint8Array }>
        signTransaction(transaction: web3.Transaction): Promise<web3.Transaction>
        signAllTransactions(transactions: web3.Transaction[]): Promise<web3.Transaction[]>
        on(event: string, callback: (args: any) => void): void
        isPhantom?: boolean
      }
    }
  }
}

export class PhantomConnector extends Connector {
  readonly id = 'phantom'
  readonly name = 'Phantom'
  readonly ready = typeof window !== 'undefined' && !!window.phantom?.solana

  #provider?: WindowProvider
  #publicKey?: string

  constructor(config: { chains?: Chain[] }) {
    super(config)
  }

  async connect(): Promise<ConnectorData> {
    try {
      const provider = await this.getProvider()
      if (!provider) throw new Error('Provider not found')

      const account = await this.getAccount()
      if (!account) throw new Error('Failed to get account')

      // Sign a message to verify ownership
      const message = new TextEncoder().encode(`Wallet Tree Authentication\nNonce: ${Date.now()}`)
      const { signature } = await provider.signMessage(message)
      const signatureBase64 = Buffer.from(signature).toString('base64')

      // Store the signature for later use in authentication
      this.#publicKey = account

      return {
        account,
        chain: { id: 1, unsupported: false },
        provider: {
          ...provider,
          signatureBase64,
          message: account, // We'll use the public key as the message for auth
          walletType: 'solana',
        },
      }
    } catch (error) {
      throw error
    }
  }

  async disconnect(): Promise<void> {
    const provider = await this.getProvider()
    if (!provider) return
    await provider.disconnect()
    this.#publicKey = undefined
  }

  async getAccount(): Promise<string> {
    if (this.#publicKey) return this.#publicKey
    const provider = await this.getProvider()
    if (!provider) throw new Error('Provider not found')
    const response = await provider.connect()
    const publicKey = response.publicKey.toString()
    this.#publicKey = publicKey
    return publicKey
  }

  async getProvider(): Promise<typeof window.phantom.solana> {
    if (this.#provider) return this.#provider

    if (typeof window === 'undefined') throw new Error('Window is undefined')
    if (!window.phantom?.solana) throw new Error('Phantom not installed')
    if (!window.phantom.solana.isPhantom) throw new Error('Invalid Phantom installation')

    this.#provider = window.phantom.solana
    return window.phantom.solana
  }

  async isAuthorized() {
    try {
      const provider = await this.getProvider()
      if (!provider) return false
      return true
    } catch {
      return false
    }
  }

  protected onAccountsChanged(accounts: string[]): void {
    if (accounts.length === 0) this.emit('disconnect')
    else this.emit('change', { account: accounts[0] })
  }

  protected onChainChanged(chain: number | string): void {
    this.emit('change', { chain: { id: Number(chain), unsupported: false } })
  }

  protected onDisconnect(): void {
    this.emit('disconnect')
  }
} 