{"name": "coinlink", "version": "0.1.0", "private": true, "scripts": {"dev": "cross-env NODE_OPTIONS='--max_old_space_size=4096' next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hello-pangea/dnd": "^18.0.1", "@radix-ui/react-dialog": "^1.0.5", "@radix-ui/react-label": "^2.0.2", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-tabs": "^1.1.3", "@solana/web3.js": "^1.98.0", "@supabase/supabase-js": "^2.49.1", "@types/uuid": "^10.0.0", "bs58": "^6.0.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "lucide-react": "^0.344.0", "mongoose": "^8.2.0", "next": "14.1.0", "next-auth": "^4.24.6", "react": "^18.2.0", "react-dom": "^18.2.0", "react-hot-toast": "^2.4.1", "siwe": "^2.1.4", "tailwind-merge": "^2.2.1", "tweetnacl": "^1.0.3", "uuid": "^11.1.0", "viem": "^1.19.11", "wagmi": "^1.4.7"}, "devDependencies": {"@babel/plugin-transform-class-properties": "^7.25.9", "@babel/plugin-transform-private-methods": "^7.25.9", "@babel/plugin-transform-private-property-in-object": "^7.25.9", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.4.17", "cross-env": "^7.0.3", "eslint": "^8.56.0", "eslint-config-next": "14.1.0", "pino-pretty": "^13.0.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "typescript": "^5"}}