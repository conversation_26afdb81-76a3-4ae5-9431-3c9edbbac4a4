'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from '@/components/ui/button'
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { StableInput, StableTextarea } from '@/components/ui/stable-input'
import { useState, useEffect, useRef } from 'react'
import { GripVertical, ExternalLink, Trash2, Camera, Twitter, Instagram, Youtube, Facebook, Rocket, CandlestickChart, Send, Search, Radio, Calendar, Copy, Plus, Link as LinkIcon, Linkedin, Globe, Github, Crosshair, Landmark, Briefcase } from 'lucide-react'
import toast from 'react-hot-toast'
import Image from 'next/image'
import { formatAddress, LINK_ICONS } from '@/lib/utils'
import { Dialog, DialogContent, DialogHeader, Di<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>ger, Di<PERSON>Footer } from '@/components/ui/dialog'
import { Switch } from '@/components/ui/switch'
import { Label } from '@/components/ui/label'
import { useDebounce } from '@/hooks/useDebounce'
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"

interface ImageData {
  url: string
  isDefault: boolean
  metadata?: {
    originalName: string
    mimeType: string
    size: number
  } | null
}

interface Profile {
  biography: string
  contractAddress?: string
  links: Record<string, string>
  avatar: ImageData
  banner: ImageData
}

interface ProfileData {
  coinProfile: Profile
  personalProfile: Profile
}

const COIN_PROFILE_LINKS = [
  { key: 'pumpfun', label: 'PumpFun' },
  { key: 'twitter', label: 'Twitter' },
  { key: 'telegram', label: 'Telegram' },
  { key: 'website', label: 'Website' },
  { key: 'chart', label: 'Chart' },
  { key: 'blockexplorer', label: 'Block Explorer' },
  { key: 'nftcollection', label: 'NFT Collection' },
  { key: 'staking', label: 'Staking' },
  { key: 'github', label: 'Github' },
  { key: 'farcaster', label: 'Farcaster' },
]

const PERSONAL_PROFILE_LINKS = [
  { key: 'twitter', label: 'Twitter' },
  { key: 'tradingbot', label: 'Trading Bot' },
  { key: 'telegram', label: 'Telegram' },
  { key: 'website', label: 'Website' },
  { key: 'github', label: 'Github' },
  { key: 'portfolio', label: 'Portfolio' },
  { key: 'instagram', label: 'Instagram' },
  { key: 'youtube', label: 'YouTube' },
  { key: 'facebook', label: 'Facebook' },
  { key: 'linkedin', label: 'LinkedIn' },
  { key: 'farcaster', label: 'Farcaster' },
]

const DEFAULT_PROFILE: Profile = {
  biography: '',
  contractAddress: '',
  links: {},
  avatar: {
    url: '/images/default-avatar.svg',
    isDefault: true,
    metadata: null
  },
  banner: {
    url: '/images/default-banner.svg',
    isDefault: true,
    metadata: null
  }
}

const ShareLinkManager = ({ isPublic, onPublicStatusChange }: { isPublic: boolean, onPublicStatusChange: (isPublic: boolean) => void }) => {
  const [links, setLinks] = useState<Array<{ id: string; createdAt: string; expiresAt: string | null }>>([])
  const [isLoading, setIsLoading] = useState(false)
  const { data: session } = useSession()

  useEffect(() => {
    fetchLinks()
  }, [])

  const fetchLinks = async () => {
    try {
      const response = await fetch('/api/share-link')
      if (!response.ok) throw new Error('Failed to fetch links')
      const data = await response.json()
      setLinks(data)
    } catch (error) {
      console.error('Error fetching links:', error)
      toast.error('Failed to fetch share links')
    }
  }

  const handleCreateLink = async (expiresAt?: string) => {
    try {
      setIsLoading(true)
      const response = await fetch('/api/share-link', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ expiresAt })
      })

      if (!response.ok) throw new Error('Failed to create link')
      
      await fetchLinks()
      toast.success('Share link created successfully')
    } catch (error) {
      console.error('Error creating link:', error)
      toast.error('Failed to create share link')
    } finally {
      setIsLoading(false)
    }
  }

  const handleDeleteLink = async (linkId: string) => {
    try {
      const response = await fetch('/api/share-link', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ linkId })
      })

      if (!response.ok) throw new Error('Failed to delete link')
      
      await fetchLinks()
      toast.success('Share link deleted successfully')
    } catch (error) {
      console.error('Error deleting link:', error)
      toast.error('Failed to delete share link')
    }
  }

  const copyToClipboard = (type: 'share' | 'public', value: string) => {
    const url = type === 'share' 
      ? `${window.location.origin}/share/${value}`
      : `${window.location.origin}/${value}`
    navigator.clipboard.writeText(url)
    toast.success('Link copied to clipboard')
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Profile Sharing</h2>
      </div>

      <div className="flex items-center space-x-4 p-4 bg-muted backdrop-blur-sm rounded-lg">
        <div className="flex-1">
          <Label htmlFor="public-profile">Public Profile</Label>
          <p className="text-sm text-muted-foreground">Make your profile permanently visible to everyone at /{session?.user?.username}</p>
        </div>
        <Switch
          id="public-profile"
          checked={isPublic}
          onCheckedChange={onPublicStatusChange}
        />
      </div>

      {isPublic && (
        <div className="flex items-center justify-between p-4 bg-muted backdrop-blur-sm rounded-lg">
          <div className="flex items-center gap-2">
            <span className="text-sm">Public URL:</span>
            <code className="text-sm">/{session?.user?.username}</code>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => copyToClipboard('public', session?.user?.username || '')}
          >
            <Copy className="w-4 h-4" />
          </Button>
        </div>
      )}

      <div className="border-t pt-4">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold">Temporary Share Links</h3>
            <p className="text-sm text-muted-foreground">Create a temporary link to share your profile for a selected duration</p>
          </div>
          <Dialog>
            <DialogTrigger asChild>
              <Button variant="outline">Create Share Link</Button>
            </DialogTrigger>
            <DialogContent>
              <DialogHeader>
                <DialogTitle>Create Share Link</DialogTitle>
              </DialogHeader>
              <div className="space-y-4 pt-4">
                <Button
                  className="w-full"
                  onClick={() => handleCreateLink()}
                  disabled={isLoading}
                >
                  Create Permanent Link
                </Button>
                <Button
                  className="w-full"
                  onClick={() => {
                    const date = new Date()
                    date.setDate(date.getDate() + 7)
                    handleCreateLink(date.toISOString())
                  }}
                  disabled={isLoading}
                >
                  Create 7-Day Link
                </Button>
                <Button
                  className="w-full"
                  onClick={() => {
                    const date = new Date()
                    date.setDate(date.getDate() + 30)
                    handleCreateLink(date.toISOString())
                  }}
                  disabled={isLoading}
                >
                  Create 30-Day Link
                </Button>
              </div>
            </DialogContent>
          </Dialog>
        </div>

        {links.length > 0 ? (
          <div className="space-y-4 mt-4">
            {links.map((link) => (
              <div
                key={link.id}
                className="flex items-center justify-between p-4 bg-card/50 backdrop-blur-sm rounded-lg"
              >
                <div className="flex items-center gap-4">
                  <Calendar className="w-4 h-4 text-muted-foreground" />
                  <div>
                    <p className="text-sm">
                      Created: {new Date(link.createdAt).toLocaleDateString()}
                    </p>
                    {link.expiresAt && (
                      <p className="text-xs text-muted-foreground">
                        Expires: {new Date(link.expiresAt).toLocaleDateString()}
                      </p>
                    )}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => copyToClipboard('share', link.id)}
                  >
                    <Copy className="w-4 h-4" />
                  </Button>
                  <Button
                    variant="ghost"
                    size="icon"
                    onClick={() => handleDeleteLink(link.id)}
                  >
                    <Trash2 className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-muted-foreground">
            No share links created yet
          </div>
        )}
      </div>
    </div>
  )
}

const Dashboard = () => {
  const { data: session } = useSession()
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [isPublic, setIsPublic] = useState(false)
  const [profiles, setProfiles] = useState<ProfileData>({
    coinProfile: DEFAULT_PROFILE,
    personalProfile: DEFAULT_PROFILE,
  })
  const [localProfiles, setLocalProfiles] = useState<ProfileData>({
    coinProfile: DEFAULT_PROFILE,
    personalProfile: DEFAULT_PROFILE,
  })
  const avatarInputRef = useRef<HTMLInputElement>(null)
  const bannerInputRef = useRef<HTMLInputElement>(null)

  // Create debounced save functions
  const debouncedSave = useDebounce(async (type: 'coin' | 'personal', data: any) => {
    try {
      console.log('Debounced save - type:', type, 'data:', data)

      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          [`${type}Profile`]: data
        })
      })

      if (!response.ok) throw new Error('Failed to save profile')
      
      // Update the main profile state only after successful save
      setProfiles(prev => ({
        ...prev,
        [`${type}Profile`]: data
      }))
    } catch (error) {
      console.error('Error saving profile:', error)
      toast.error('Failed to save changes')
    }
  }, 1000)

  useEffect(() => {
    if (!session?.user) {
      router.push('/')
      return
    }
    fetchProfiles()
    fetchPublicStatus()
  }, [session])

  useEffect(() => {
    setLocalProfiles(profiles)
  }, [profiles])

  const fetchPublicStatus = async () => {
    try {
      const response = await fetch('/api/user/profile')
      if (!response.ok) throw new Error('Failed to fetch profile')
      const data = await response.json()
      setIsPublic(data.isPublic)
    } catch (error) {
      console.error('Error fetching public status:', error)
    }
  }

  const handlePublicStatusChange = async (newStatus: boolean) => {
    try {
      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ isPublic: newStatus })
      })

      if (!response.ok) throw new Error('Failed to update profile')
      setIsPublic(newStatus)
      toast.success(newStatus ? 'Profile set to public' : 'Profile set to private')
    } catch (error) {
      console.error('Error updating public status:', error)
      toast.error('Failed to update profile visibility')
    }
  }

  const fetchProfiles = async () => {
    try {
      const response = await fetch('/api/user/profile')
      if (!response.ok) throw new Error('Failed to fetch profiles')
      const data = await response.json()
      
      // Ensure proper structure for both profiles
      const normalizedData: ProfileData = {
        coinProfile: {
          biography: data.coinProfile?.biography || '',
          contractAddress: data.coinProfile?.contractAddress || '',
          links: data.coinProfile?.links || {},
          avatar: {
            url: data.coinProfile?.avatar?.url || DEFAULT_PROFILE.avatar.url,
            isDefault: data.coinProfile?.avatar?.isDefault ?? true,
            metadata: data.coinProfile?.avatar?.metadata || null
          },
          banner: {
            url: data.coinProfile?.banner?.url || DEFAULT_PROFILE.banner.url,
            isDefault: data.coinProfile?.banner?.isDefault ?? true,
            metadata: data.coinProfile?.banner?.metadata || null
          }
        },
        personalProfile: {
          biography: data.personalProfile?.biography || '',
          contractAddress: data.personalProfile?.contractAddress || '',
          links: data.personalProfile?.links || {},
          avatar: {
            url: data.personalProfile?.avatar?.url || DEFAULT_PROFILE.avatar.url,
            isDefault: data.personalProfile?.avatar?.isDefault ?? true,
            metadata: data.personalProfile?.avatar?.metadata || null
          },
          banner: {
            url: data.personalProfile?.banner?.url || DEFAULT_PROFILE.banner.url,
            isDefault: data.personalProfile?.banner?.isDefault ?? true,
            metadata: data.personalProfile?.banner?.metadata || null
          }
        }
      }

      setProfiles(normalizedData)
      setLocalProfiles(normalizedData)
    } catch (error) {
      console.error('Error fetching profiles:', error)
      toast.error('Failed to load profiles')
    }
  }

  const handleImageUpload = async (file: File, type: 'avatar' | 'banner', profileType: 'coin' | 'personal') => {
    try {
      const formData = new FormData()
      formData.append('file', file)
      formData.append('type', type)
      formData.append('profileType', profileType)

      const response = await fetch('/api/upload', {
        method: 'POST',
        body: formData,
      })

      if (!response.ok) throw new Error('Failed to upload image')
      
      const { url, metadata } = await response.json()
      
      setProfiles(prev => ({
        ...prev,
        [`${profileType}Profile`]: {
          ...prev[`${profileType}Profile`],
          [type]: {
            url,
            isDefault: false,
            metadata: {
              originalName: metadata.originalName,
              mimeType: metadata.mimeType,
              size: metadata.size
            }
          }
        }
      }))

      // Save the profile with the new image
      await handleSaveProfile(profileType)
      
      toast.success('Image uploaded successfully')
    } catch (error) {
      console.error('Error uploading image:', error)
      toast.error('Failed to upload image')
    }
  }

  const handleSaveProfile = async (type: 'coin' | 'personal') => {
    setIsLoading(true)
    try {
      const profileData = localProfiles[`${type}Profile`]
      console.log('Saving profile data:', profileData)

      const response = await fetch('/api/user/profile', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          [type === 'coin' ? 'coinProfile' : 'personalProfile']: profileData
        })
      })

      if (!response.ok) throw new Error('Failed to save profile')

      // Update the main profile state with the saved data
      setProfiles(prev => ({
        ...prev,
        [`${type}Profile`]: localProfiles[`${type}Profile`]
      }))

      toast.success('Profile saved successfully')
    } catch (error) {
      console.error('Error saving profile:', error)
      toast.error('Failed to save profile')
    } finally {
      setIsLoading(false)
    }
  }

  const handleBiographyChange = (type: 'coin' | 'personal', value: string) => {
    setLocalProfiles(prev => {
      const newState = {
        ...prev,
        [`${type}Profile`]: {
          ...prev[`${type}Profile`],
          biography: value
        }
      }

      // Trigger debounced save with the new state
      debouncedSave(type, newState[`${type}Profile`])

      return newState
    })
  }

  const handleContractAddressChange = (type: 'coin' | 'personal', value: string) => {
    setLocalProfiles(prev => {
      const newState = {
        ...prev,
        [`${type}Profile`]: {
          ...prev[`${type}Profile`],
          contractAddress: value
        }
      }

      // Trigger debounced save with the new state
      debouncedSave(type, newState[`${type}Profile`])

      return newState
    })
  }

  const handleLinkChange = (type: 'coin' | 'personal', key: string, value: string) => {
    setLocalProfiles(prev => {
      const newState = {
        ...prev,
        [`${type}Profile`]: {
          ...prev[`${type}Profile`],
          links: {
            ...prev[`${type}Profile`].links,
            [key]: value
          }
        }
      }
      
      // Trigger debounced save with the new state
      debouncedSave(type, newState[`${type}Profile`])
      
      return newState
    })
  }

  const handleRemoveLink = (type: 'coin' | 'personal', key: string) => {
    setLocalProfiles(prev => {
      const newState = {
        ...prev,
        [`${type}Profile`]: {
          ...prev[`${type}Profile`],
          links: {
            ...prev[`${type}Profile`].links
          }
        }
      }
      delete newState[`${type}Profile`].links[key]
      
      // Trigger debounced save with the new state
      debouncedSave(type, newState[`${type}Profile`])
      
      return newState
    })
  }

  const ProfileContent = ({ type }: { type: 'coin' | 'personal' }) => {
    const profile = localProfiles[`${type}Profile`]
    const links = type === 'coin' ? COIN_PROFILE_LINKS : PERSONAL_PROFILE_LINKS
    const [showAddLinkDialog, setShowAddLinkDialog] = useState(false)
    const [newLinkKey, setNewLinkKey] = useState('')
    const [newLinkUrl, setNewLinkUrl] = useState('')
    const [customLinks, setCustomLinks] = useState<Array<{ key: string; label: string }>>([])

    // Load custom links on component mount
    useEffect(() => {
      // Extract custom links from profile that aren't in the predefined lists
      const predefinedKeys = (type === 'coin' ? COIN_PROFILE_LINKS : PERSONAL_PROFILE_LINKS).map(link => link.key)
      const custom = Object.keys(profile.links || {})
        .filter(key => !predefinedKeys.includes(key))
        .map(key => ({
          key,
          label: key.charAt(0).toUpperCase() + key.slice(1) // Capitalize first letter
        }))
      
      setCustomLinks(custom)
    }, [profile.links, type])

    const handleAddLink = () => {
      if (!newLinkKey) {
        toast.error('Please provide a key for the link')
        return
      }

      // Check if key already exists
      const allLinks = [...links, ...customLinks]
      if (allLinks.some(link => link.key === newLinkKey)) {
        toast.error('A link with this key already exists')
        return
      }

      // Add to custom links
      setCustomLinks(prev => [...prev, { key: newLinkKey, label: newLinkKey.charAt(0).toUpperCase() + newLinkKey.slice(1) }])
      
      // Add to profile links if URL is provided
      if (newLinkUrl) {
        handleLinkChange(type, newLinkKey, newLinkUrl)
      }

      // Reset form
      setNewLinkKey('')
      setNewLinkUrl('')
      setShowAddLinkDialog(false)
      
      toast.success('Link added successfully')
    }

    const handleRemoveCustomLink = (key: string) => {
      // Remove from custom links
      setCustomLinks(prev => prev.filter(link => link.key !== key))
      
      // Remove from profile links using the parent's handleRemoveLink
      handleRemoveLink(type, key)
      
      toast.success('Link removed')
    }

    // Combine predefined and custom links
    const allLinks = [...links, ...customLinks]

    return (
      <div className="space-y-8">
        <div className="relative w-full h-48 bg-gray-200 rounded-lg overflow-hidden group">
          {profile?.banner?.url && (
            <Image
              src={profile.banner.url}
              alt="Profile Banner"
              fill
              className="object-cover"
            />
          )}
          <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
            <Button 
              variant="default" 
              className="opacity-0 group-hover:opacity-100 transition-opacity"
              onClick={() => bannerInputRef.current?.click()}
            >
              <Camera className="w-4 h-4 mr-2" />
              Change Banner
            </Button>
          </div>
          <input
            ref={bannerInputRef}
            type="file"
            accept="image/jpeg,image/png,image/webp,image/gif,image/avif,image/svg+xml"
            className="hidden"
            onChange={(e) => {
              const file = e.target.files?.[0]
              if (file) handleImageUpload(file, 'banner', type)
            }}
          />
        </div>

        <div className="flex items-start gap-8">
          <div className="relative w-24 h-24 bg-gray-200 rounded-full overflow-hidden group">
            {profile?.avatar?.url && (
              <Image
                src={profile.avatar.url}
                alt="Profile Avatar"
                fill
                className="object-cover"
              />
            )}
            <div className="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
              <Button 
                variant="default" 
                className="opacity-0 group-hover:opacity-100 transition-opacity"
                onClick={() => avatarInputRef.current?.click()}
              >
                <Camera className="w-4 h-4" />
              </Button>
            </div>
            <input
              ref={avatarInputRef}
              type="file"
              accept="image/jpeg,image/png,image/webp,image/gif,image/avif,image/svg+xml"
              className="hidden"
              onChange={(e) => {
                const file = e.target.files?.[0]
                if (file) handleImageUpload(file, 'avatar', type)
              }}
            />
          </div>
          <div>
            <h1 className="text-3xl font-bold">@{session?.user?.username}</h1>
            <p className="text-sm text-muted-foreground">
              {session?.user?.walletAddress && formatAddress(session.user.walletAddress)}
            </p>
          </div>
        </div>

        <div className="space-y-4">
          <h2 className="text-xl font-semibold">Biography</h2>
          <StableTextarea
            placeholder="Tell us about yourself..."
            initialValue={profile.biography || ''}
            onValueChange={(value) => handleBiographyChange(type, value)}
            className="min-h-[100px]"
          />
        </div>

        {/* Contract Address - Only for coin profiles */}
        {type === 'coin' && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Contract Address</h2>
            <StableInput
              placeholder="Enter contract address (e.g., 0x... or token address)"
              initialValue={profile.contractAddress || ''}
              onValueChange={(value) => handleContractAddressChange(type, value)}
              className="font-mono text-sm"
            />
          </div>
        )}

        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <h2 className="text-xl font-semibold">Links</h2>
            <Dialog open={showAddLinkDialog} onOpenChange={setShowAddLinkDialog}>
              <DialogTrigger asChild>
                <Button variant="default">
                  <Plus className="w-4 h-4 mr-2" />
                  Add Link
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Add Custom Link</DialogTitle>
                </DialogHeader>
                <div className="space-y-4 py-4">
                  <div className="space-y-2">
                    <Label htmlFor="linkKey">Link Key (no spaces, lowercase)</Label>
                    <Input
                      id="linkKey"
                      placeholder="e.g. discord"
                      value={newLinkKey}
                      onChange={(e) => setNewLinkKey(e.target.value.toLowerCase().replace(/\s+/g, ''))}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="linkUrl">Link URL (optional)</Label>
                    <Input
                      id="linkUrl"
                      placeholder="e.g. https://discord.gg/your-server"
                      value={newLinkUrl}
                      onChange={(e) => setNewLinkUrl(e.target.value)}
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowAddLinkDialog(false)}>Cancel</Button>
                  <Button onClick={handleAddLink}>Add Link</Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>

          <div className="space-y-2">
            {allLinks.map(({ key, label }) => {
              const url = profile.links[key] || '';
              let displayUrl = '';
              try {
                if (url) {
                  const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
                  displayUrl = urlObj.hostname.replace(/^www\./, '');
                }
              } catch (e) {
                displayUrl = url;
              }

              // Use a default icon for custom links
              const Icon = LINK_ICONS[key] || LinkIcon;
              const isCustomLink = customLinks.some(link => link.key === key);

              return (
                <div key={key} className="flex items-center gap-2">
                  <GripVertical className="w-4 h-4 text-gray-400" />
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      {Icon && <Icon className="w-4 h-4" />}
                      <span className="font-medium">{label}</span>
                      {url && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 hover:bg-accent/20"
                          onClick={() => handleRemoveCustomLink(key)}
                        >
                          <Trash2 className="w-3 h-3 text-red-500" />
                        </Button>
                      )}
                    </div>
                    <div className="flex items-center gap-2">
                      <StableInput
                        placeholder={`Enter ${label} URL`}
                        initialValue={url}
                        onValueChange={(value) => handleLinkChange(type, key, value)}
                      />
                      {url && (
                        <div className="flex items-center gap-2">
                          <span className="hidden md:block text-sm text-gray-500">{displayUrl}</span>
                          <a
                            href={url.startsWith('http') ? url : `https://${url}`}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="text-accent hover:text-accent/80"
                          >
                            <ExternalLink className="w-4 h-4" />
                          </a>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        <ShareLinkManager isPublic={isPublic} onPublicStatusChange={handlePublicStatusChange} />

        <div className="space-y-2">
          <Button
            onClick={() => {
              console.log('Current localProfiles state:', localProfiles)
              console.log('Current profiles state:', profiles)
            }}
            variant="outline"
            className="w-full"
          >
            Debug: Log Current State
          </Button>
          <Button
            onClick={() => handleSaveProfile(type)}
            disabled={isLoading}
            className="w-full text-white"
          >
            {isLoading ? 'Saving...' : 'Save Changes'}
          </Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/80">
      {/* Main Content */}
      <main className="container mx-auto px-4 pt-32 pb-20">
        <div className="max-w-4xl mx-auto">
          <Tabs defaultValue="coin" className="w-full">
            <TabsList className="grid w-full grid-cols-2 mb-8">
              <TabsTrigger value="coin">Coin Profile</TabsTrigger>
              <TabsTrigger value="personal">Creator Profile</TabsTrigger>
            </TabsList>

            <TabsContent value="coin">
              <ProfileContent type="coin" />
            </TabsContent>

            <TabsContent value="personal">
              <ProfileContent type="personal" />
            </TabsContent>
          </Tabs>
        </div>
      </main>
      <Footer />
    </div>
  )
}

export default Dashboard 