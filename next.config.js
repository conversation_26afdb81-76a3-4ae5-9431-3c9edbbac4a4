/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: '**',
      },
    ],
  },
  webpack: (config, { dev, isServer }) => {
    // Add performance optimizations
    config.resolve.fallback = { 
      fs: false, 
      net: false, 
      tls: false 
    };

    // Optimize development performance
    if (dev && !isServer) {
      config.optimization = {
        ...config.optimization,
        splitChunks: {
          chunks: 'all',
          minSize: 20000,
          maxSize: 244000,
          cacheGroups: {
            commons: {
              test: /[\\/]node_modules[\\/]/,
              name: 'vendors',
              chunks: 'all',
              priority: -10
            },
            default: {
              minChunks: 2,
              priority: -20,
              reuseExistingChunk: true
            }
          }
        },
        runtimeChunk: {
          name: 'runtime',
        }
      };
    }

    return config;
  },
  // Add these settings to help with runtime performance
  poweredByHeader: false,
  generateEtags: false,
  compress: true,
  productionBrowserSourceMaps: false,
}

// Add production-specific settings
if (process.env.NODE_ENV === 'production') {
  Object.assign(nextConfig, {
    typescript: {
      ignoreBuildErrors: true,
    },
    eslint: {
      ignoreDuringBuilds: true,
    },
    swcMinify: false,
    experimental: {
      forceSwcTransforms: false
    },
    output: 'standalone',
    staticPageGenerationTimeout: 1000
  })
}

module.exports = nextConfig 