# CoinLink

A Next.js application for managing user profiles with cryptocurrency wallet integration, allowing users to receive tips via Ethereum, Solana, and Bitcoin.

## Getting Started

First, run the development server:

```bash
npm run dev
# or
yarn dev
# or
pnpm dev
# or
bun dev
```

Open [http://localhost:3000](http://localhost:3000) with your browser to see the result.

You can start editing the page by modifying `app/page.tsx`. The page auto-updates as you edit the file.

This project uses [`next/font`](https://nextjs.org/docs/app/building-your-application/optimizing/fonts) to automatically optimize and load [<PERSON>eist](https://vercel.com/font), a new font family for Vercel.

## Features

- User profiles with customizable information
- Cryptocurrency wallet integration (Ethereum, Solana, Bitcoin)
- Tip jar functionality for receiving cryptocurrency tips
- Responsive design for mobile and desktop

## Docker Deployment

This project includes Docker configuration for easy deployment:

```bash
# Build the Docker image
docker build -t wallet-tree .

# Run the Docker container
docker run -p 3000:3000 wallet-tree
```

For detailed deployment instructions, see the [DEPLOYMENT.md](./DEPLOYMENT.md) guide.

## Environment Variables

Create a `.env.local` file in the root directory with the following variables:

```
MONGODB_URI=your_mongodb_connection_string
NEXTAUTH_SECRET=your_nextauth_secret
NEXTAUTH_URL=http://localhost:3000
NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID=your_walletconnect_project_id
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key
```

## Learn More

To learn more about Next.js, take a look at the following resources:

- [Next.js Documentation](https://nextjs.org/docs) - learn about Next.js features and API.
- [Learn Next.js](https://nextjs.org/learn) - an interactive Next.js tutorial.

## Deploy on Vercel

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

This project includes a `vercel.json` configuration file for Docker-based deployment on Vercel.

Check out our [Next.js deployment documentation](https://nextjs.org/docs/app/building-your-application/deploying) for more details.
