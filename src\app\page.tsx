'use client'

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useState, useEffect } from 'react'
import { useAccount } from 'wagmi'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import toast from 'react-hot-toast'
import { WalletConnectModal } from "@/components/wallet-connect-modal"
import Link from "next/link"
import { Input } from "@/components/ui/input"
import { Navbar } from "@/components/navbar"
import { Footer } from "@/components/footer"
import Image from "next/image"
import nacl from 'tweetnacl'
import bs58 from 'bs58'
import { decryptPhantomPayload } from '@/lib/utils'

export default function Home() {
  const { isConnected } = useAccount()
  const { data: session } = useSession()
  const router = useRouter()
  const searchParams = useSearchParams()
  const [username, setUsername] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [showWalletModal, setShowWalletModal] = useState(false)
  const [needsUsername, setNeedsUsername] = useState(false)

  // Handle mobile wallet redirects and URL parameters
  useEffect(() => {
    const handleWalletRedirect = async () => {
      // Check for Phantom redirect parameters
      const phantomPublicKey = searchParams.get('phantom_encryption_public_key')
      const phantomData = searchParams.get('data')
      const phantomNonce = searchParams.get('nonce')
      const errorCode = searchParams.get('errorCode')
      const errorMessage = searchParams.get('errorMessage')

      // Handle errors first
      if (errorCode) {
        toast.error(errorMessage || 'Wallet connection failed')
        return
      }

      // Handle Phantom connection response
      if (phantomPublicKey && phantomData && phantomNonce) {
        try {
          // Retrieve stored keypair
          const storedKeyPair = localStorage.getItem('dappKeyPair')
          if (!storedKeyPair) {
            toast.error('Connection session expired. Please try again.')
            return
          }

          const keyPair = JSON.parse(storedKeyPair)
          const dappSecretKey = bs58.decode(keyPair.secretKey)
          const phantomPubKey = bs58.decode(phantomPublicKey)

          // Create shared secret
          const sharedSecret = nacl.box.before(phantomPubKey, dappSecretKey)

          // Decrypt the response using utility function
          const connectData = decryptPhantomPayload(phantomData, phantomNonce, sharedSecret)

          // Store session and trigger authentication
          localStorage.setItem('phantomSession', connectData.session)

          // Get stored username
          const storedUsername = localStorage.getItem('phantomUsername')
          if (storedUsername && connectData.public_key) {
            // Trigger the wallet connection modal with the wallet data
            const event = new CustomEvent('phantom-wallet-callback', {
              detail: {
                public_key: connectData.public_key,
                session: connectData.session
              }
            })
            window.dispatchEvent(event)
          }

          // Clean up URL parameters
          const url = new URL(window.location.href)
          url.searchParams.delete('phantom_encryption_public_key')
          url.searchParams.delete('data')
          url.searchParams.delete('nonce')
          window.history.replaceState({}, '', url.toString())

        } catch (error) {
          console.error('Error processing Phantom redirect:', error)
          toast.error('Failed to process wallet connection')
        }
      }

      // Check if user returned from wallet app without URL parameters
      const checkWalletReturn = () => {
        const phantomAttempt = localStorage.getItem('dappKeyPair')
        const metamaskAttempt = localStorage.getItem('metamaskConnectionAttempt')

        if (phantomAttempt || metamaskAttempt) {
          // User might have returned from wallet app, show modal if not already shown
          setTimeout(() => {
            if (!showWalletModal && !session?.user?.walletAddress) {
              setShowWalletModal(true)
            }
          }, 1000)
        }
      }

      checkWalletReturn()
    }

    handleWalletRedirect()
  }, [searchParams, showWalletModal, session])

  // Add event listener for needsUsername event
  useEffect(() => {
    const handleNeedsUsername = () => {
      setNeedsUsername(true)
    }

    window.addEventListener('needsUsername', handleNeedsUsername)
    return () => {
      window.removeEventListener('needsUsername', handleNeedsUsername)
    }
  }, [])

  const handleConnectWallet = () => {
    if (!username) {
      toast.error('Please enter a username first')
      return
    }

    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_-]{3,16}$/
    if (!usernameRegex.test(username)) {
      toast.error('Username must be 3-16 characters and can only contain letters, numbers, underscores, and hyphens')
      return
    }

    setShowWalletModal(true)
  }

  // Redirect to dashboard if already authenticated
  if (session?.user?.walletAddress) {
    router.push('/dashboard')
    return null
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-background to-background/80 relative overflow-hidden">
      {/* Background Logo */}
      <div className="absolute top-11 right-[-100px] pointer-events-none z-0">
        <Image
          src="/images/coinlink-pixel-logo.png"
          alt="CoinLink Logo"
          width={600}
          height={600}
          className="opacity-15 w-auto h-auto min-w-[400px] md:min-w-[800px] min-h-[338px] md:min-h-[677px]"
          priority
        />
      </div>
      
      <Navbar onConnectWallet={handleConnectWallet} isConnected={isConnected} />
      
      {/* Main Content */}
      <main className="container mx-auto px-4 pt-32 pb-20 relative z-10">
        <div className="max-w-3xl mx-auto text-center">
          <div className="flex justify-center mb-8">
            <div className="relative">
              <h2 className="text-4xl md:text-6xl font-bold mb-6">
                coinlink.gg/
                <span className="text-gradient-primary">username</span>
              </h2>
            </div>
          </div>

          <div className="max-w-md mx-auto space-y-4">
            <Input
              type="text"
              placeholder="Enter your username"
              value={username}
              onChange={(e) => setUsername(e.target.value)}
              className="text-lg h-12 text-center bg-white/5"
              disabled={isLoading}
            />
            <Button
              size="lg"
              className="w-full bg-gradient-primary"
              onClick={handleConnectWallet}
              disabled={isConnected || isLoading}
            >
              {isConnected ? 'Connected' : 'Connect Wallet'}
            </Button>
            <p className="text-sm text-muted-foreground">
              Enter a username and connect your wallet to create or access your Web3 profile
            </p>
          </div>

          {/* Features Section */}
          <div className="mt-20 grid md:grid-cols-2 gap-8">
            <div className="glass-effect p-6 rounded-lg text-center">
              <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Dual Profiles</h3>
              <p className="text-sm text-muted-foreground">
                Choose a Coin or Creator Profile, or both. This lets you share your product and showcase yourself. 
              </p>
            </div>

            <div className="glass-effect p-6 rounded-lg text-center">
              <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Customizable</h3>
              <p className="text-sm text-muted-foreground">
                Customize both of your profiles with different links, images, and bios.
              </p>
            </div>

            <div className="glass-effect p-6 rounded-lg text-center">
              <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-4">
                <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Smart Links</h3>
              <p className="text-sm text-muted-foreground">
                Organize links for both your coin and personal presence to keep them streamlined.
              </p>
            </div>

            <div className="glass-effect p-6 rounded-lg text-center">
              <div className="w-12 h-12 bg-gradient-primary rounded-lg flex items-center justify-center mx-auto mb-4">
              <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-hand-coins">
                <path d="M11 15h2a2 2 0 1 0 0-4h-3c-.6 0-1.1.2-1.4.6L3 17"/><path d="m7 21 1.6-1.4c.3-.4.8-.6 1.4-.6h4c1.1 0 2.1-.4 2.8-1.2l4.6-4.4a2 2 0 0 0-2.75-2.91l-4.2 3.9"/><path d="m2 16 6 6"/><circle cx="16" cy="9" r="2.9"/><circle cx="6" cy="5" r="3"/></svg>
              </div>
              <h3 className="text-lg font-semibold mb-2">Tip Jar</h3>
              <p className="text-sm text-muted-foreground">
                Built in tipping functionality, sent to the wallet you're logged in with.
              </p>
            </div>
          </div>
        </div>
      </main>
      <Footer />

      <WalletConnectModal
        open={showWalletModal}
        onOpenChange={setShowWalletModal}
        username={username}
      />
    </div>
  )
}
