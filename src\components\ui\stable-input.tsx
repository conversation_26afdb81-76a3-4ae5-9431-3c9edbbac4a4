'use client'

import React, { useState, useEffect, useRef } from 'react'
import { Input } from './input'
import { Textarea } from './textarea'

interface StableInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  onValueChange: (value: string) => void
  initialValue: string
  debounceTime?: number
}

interface StableTextareaProps extends React.TextareaHTMLAttributes<HTMLTextAreaElement> {
  onValueChange: (value: string) => void
  initialValue: string
  debounceTime?: number
}

export function StableInput({
  onValueChange,
  initialValue,
  debounceTime = 1000,
  ...props
}: StableInputProps) {
  const [value, setValue] = useState(initialValue)
  const timeoutRef = useRef<NodeJS.Timeout>()

  // Update local value when initialValue changes (e.g., from parent)
  useEffect(() => {
    setValue(initialValue)
  }, [initialValue])

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value
    setValue(newValue)

    // Debounce the callback to parent
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      onValueChange(newValue)
    }, debounceTime)
  }

  return (
    <Input
      {...props}
      value={value}
      onChange={handleChange}
    />
  )
}

export function StableTextarea({
  onValueChange,
  initialValue,
  debounceTime = 1000,
  ...props
}: StableTextareaProps) {
  const [value, setValue] = useState(initialValue)
  const timeoutRef = useRef<NodeJS.Timeout>()

  // Update local value when initialValue changes (e.g., from parent)
  useEffect(() => {
    setValue(initialValue)
  }, [initialValue])

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  const handleChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newValue = e.target.value
    setValue(newValue)

    // Debounce the callback to parent
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }

    timeoutRef.current = setTimeout(() => {
      onValueChange(newValue)
    }, debounceTime)
  }

  return (
    <Textarea
      {...props}
      value={value}
      onChange={handleChange}
    />
  )
} 