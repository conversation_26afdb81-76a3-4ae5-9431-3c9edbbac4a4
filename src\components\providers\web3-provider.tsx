'use client'

import { WagmiConfig, createConfig, configureChains } from 'wagmi'
import { mainnet } from 'wagmi/chains'
import { MetaMaskConnector } from 'wagmi/connectors/metaMask'
import { WalletConnectConnector } from 'wagmi/connectors/walletConnect'
import { PhantomConnector } from '@/lib/connectors/phantom'
import { publicProvider } from 'wagmi/providers/public'
import { jsonRpcProvider } from 'wagmi/providers/jsonRpc'

const ETHEREUM_RPC_ENDPOINT = "https://ethereum-mainnet.rpc.extrnode.com/03cc9198-9be5-45d0-9c80-d4dd4d9be811"

const { chains, publicClient, webSocketPublicClient } = configureChains(
  [mainnet],
  [
    jsonRpcProvider({
      rpc: () => ({
        http: ETHEREUM_RPC_ENDPOINT,
      }),
    }),
    publicProvider(),
  ],
)

const config = createConfig({
  autoConnect: true,
  connectors: [
    new MetaMaskConnector({ chains }),
    new WalletConnectConnector({
      chains,
      options: {
        projectId: process.env.NEXT_PUBLIC_WALLETCONNECT_PROJECT_ID || '',
        metadata: {
          name: 'CoinLink',
          description: 'Web3 Social Platform',
          url: 'https://coinlink.gg',
          icons: ['https://coinlink.gg/logo.png']
        },
      },
    }),
    new PhantomConnector({ chains }),
  ],
  publicClient,
  webSocketPublicClient,
})

export function Web3Provider({ children }: { children: React.ReactNode }) {
  return <WagmiConfig config={config}>{children}</WagmiConfig>
} 