"use client"

import { Navbar } from "@/components/navbar"
import { But<PERSON> } from "@/components/ui/button"
import { Footer } from "@/components/footer"
import Link from "next/link"

export default function FeaturesPage() {
  return (
    <div className="min-h-screen bg-background">
      <Navbar showConnectButton={false} />
      
      <main className="pt-24 pb-16">
        <div className="container mx-auto px-4">
          <div className="text-left mb-12">
            <h1 className="text-4xl font-bold mb-4">Features</h1>
            <p className="text-xl text-muted-foreground max-w-3xl">
              What makes CoinLink the ultimate Web3 social sharing app?
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-16">
            {/* Feature 1 */}
            <div className="bg-card rounded-lg p-6 shadow-sm border border-border">
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center mb-4 text-2xl">
                🚀
              </div>
              <h3 className="text-xl font-semibold mb-2">Coin Profile</h3>
              <p className="text-muted-foreground">
                Launching a coin or a project? Create a Coin Profile to share your important links such as Twitter, Telegram, Website or Chart. 
              </p>
            </div>
            
            {/* Feature 2 */}
            <div className="bg-card rounded-lg p-6 shadow-sm border border-border">
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center mb-4 text-2xl">
                😹
              </div>
              <h3 className="text-xl font-semibold mb-2">Social Profile</h3>
              <p className="text-muted-foreground">
                Create a Social Profile as an everyday Web3 trader, developer or KOL. Share your social links, trading bot referral links and more.
              </p>
            </div>
            
            {/* Feature 3 */}
            <div className="bg-card rounded-lg p-6 shadow-sm border border-border">
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center mb-4 text-2xl">
                🔗
              </div>
              <h3 className="text-xl font-semibold mb-2">Smart Links</h3>
              <p className="text-muted-foreground">
                Utilize a helpful range of preset links to quickly share your coin project or Web3 presence, with the ability to add your own custom links.
              </p>
            </div>
            
            {/* Feature 4 */}
            <div className="bg-card rounded-lg p-6 shadow-sm border border-border">
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center mb-4 text-2xl">
                💰
              </div>
              <h3 className="text-xl font-semibold mb-2">Tip Jar</h3>
              <p className="text-muted-foreground">
                Built in tipping functionality that allows people viewing your profile to tip you on-chain. Sent directly to the wallet you're logged in with.
              </p>
            </div>
            
            {/* Feature 5 */}
            <div className="bg-card rounded-lg p-6 shadow-sm border border-border">
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center mb-4 text-2xl">
                ⛓️
              </div>
              <h3 className="text-xl font-semibold mb-2">Multi-Chain Support</h3>
              <p className="text-muted-foreground">
                Login with a Solana or Ethereum wallet such as Phantom, Metamask or WalletConnect.  Support for more blockchains will be added soon!
              </p>
            </div>
            
            {/* Feature 6 */}
            <div className="bg-card rounded-lg p-6 shadow-sm border border-border">
              <div className="h-12 w-12 bg-primary/10 rounded-full flex items-center justify-center mb-4 text-2xl">
                ⚡
              </div>
              <h3 className="text-xl font-semibold mb-2">User-Friendly Interface</h3>
              <p className="text-muted-foreground">
                An intuitive, simple design and layout that makes crypto and social link management easily accessible and streamlined for everyone.
              </p>
            </div>
          </div>
          
          <div className="text-center">
            <h2 className="text-3xl font-bold mb-6">Ready to get started?</h2>
            <Link href="/">
              <Button size="lg" className="px-8">Claim Your Profile</Button>
            </Link>
          </div>
        </div>
      </main>
      
      <Footer />
    </div>
  )
} 