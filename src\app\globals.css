/* Import Inter font from Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Dark background with slight blue tint */
    --background: 230 25% 5%;
    --foreground: 210 40% 98%;

    /* Muted colors for secondary elements */
    --muted: 225 25% 12%;
    --muted-foreground: 215 20% 65%;

    /* Accent colors */
    --accent: 42 70% 40%;
    --accent-foreground: 0 0% 100%;

    /* Popover/Dialog colors */
    --popover: 230 25% 7%;
    --popover-foreground: 210 40% 98%;

    /* Borders and inputs */
    --border: 230 25% 15%;
    --input: 230 25% 15%;

    /* Card styling */
    --card: 230 25% 7%;
    --card-foreground: 210 40% 98%;

    /* Primary action colors - Ethereum inspired */
    --primary: 196 100% 50%;
    --primary-foreground: 0 0% 100%;

    /* Secondary action colors - Solana inspired */
    --secondary: 272 67% 55%;
    --secondary-foreground: 0 0% 100%;

    /* Destructive actions */
    --destructive: 0 84% 60%;
    --destructive-foreground: 210 40% 98%;

    /* Focus ring */
    --ring: 45 70% 25%;

    /* Border radius */
    --radius: 0.75rem;
  }

  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
    font-family: 'Inter', sans-serif;
  }

  /* Improved scrollbar styles */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: hsl(var(--background));
  }

  ::-webkit-scrollbar-thumb {
    background: hsl(var(--muted));
    border-radius: var(--radius);
  }

  ::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--accent));
  }
}

/* Enhanced utility classes */
@layer utilities {
  .bg-gradient-primary {
    background: linear-gradient(
      135deg,
      #ba903c,
      #c28f20,
      #b08c29
    );
  }

  .bg-gradient-secondary {
    background: linear-gradient(
      135deg,
      #9945FF,
      #14F195
    );
  }

  .text-gradient-primary {
    background: linear-gradient(
      135deg,
      #ebb13e,
      #c28f20,
      #eabd40
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .text-gradient-secondary {
    background: linear-gradient(
      to right,
      hsl(272 67% 55%),
      hsl(242 47% 50%)
    );
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
  }

  .hover-card {
    @apply transition-all duration-300;
  }

  .hover-card:hover {
    @apply shadow-lg;
    box-shadow: 0 0 20px hsla(var(--primary), 0.2);
  }

  .glass-effect {
    @apply backdrop-blur-md bg-opacity-20 border border-white/10;
    background-color: hsla(var(--background), 0.8);
  }
}
