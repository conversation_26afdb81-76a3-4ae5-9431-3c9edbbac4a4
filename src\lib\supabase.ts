import { createClient } from '@supabase/supabase-js'

if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
  throw new Error('Missing env.NEXT_PUBLIC_SUPABASE_URL')
}

if (!process.env.SUPABASE_SERVICE_ROLE_KEY) {
  throw new Error('Missing env.SUPABASE_SERVICE_ROLE_KEY')
}

// Create a Supabase client for the browser (public)
export const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
  {
    auth: {
      persistSession: false,
    },
  }
)

// Create a Supabase client for server-side operations (private)
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY,
  {
    auth: {
      persistSession: false,
      autoRefreshToken: false,
    },
  }
)

interface UploadOptions {
  userId: string;
  type: 'avatar' | 'banner';
  file: File;
  profileType: 'coin' | 'personal';
}

async function deleteOldFiles(userId: string, type: 'avatar' | 'banner', profileType: 'coin' | 'personal') {
  try {
    const path = `users/${userId}/${profileType}/${type}`
    const { data: files, error: listError } = await supabaseAdmin.storage
      .from('media')
      .list(path)

    if (listError) {
      console.error('Error listing files:', listError)
      return
    }

    if (files && files.length > 0) {
      const filePaths = files.map(file => `${path}/${file.name}`)
      const { error: deleteError } = await supabaseAdmin.storage
        .from('media')
        .remove(filePaths)

      if (deleteError) {
        console.error('Error deleting old files:', deleteError)
      }
    }
  } catch (error) {
    console.error('Error in deleteOldFiles:', error)
  }
}

export async function deleteMedia(userId: string, path: string) {
  // Verify the file belongs to the user
  if (!path.startsWith(`users/${userId}/`)) {
    throw new Error('Unauthorized: Cannot delete files owned by other users')
  }

  try {
    const { error } = await supabaseAdmin.storage
      .from('media')
      .remove([path])

    if (error) {
      throw error
    }
  } catch (error) {
    console.error('Delete error:', error)
    throw error
  }
}

export async function listUserFiles(userId: string, type: 'avatar' | 'banner', profileType: 'coin' | 'personal') {
  const path = `users/${userId}/${profileType}/${type}`
  
  try {
    const { data, error } = await supabaseAdmin.storage
      .from('media')
      .list(path)

    if (error) {
      throw error
    }

    return data
  } catch (error) {
    console.error('List files error:', error)
    throw error
  }
}

// Helper function to ensure directory exists
async function ensureDirectoryExists(userId: string, type: 'avatar' | 'banner', profileType: 'coin' | 'personal') {
  const path = `users/${userId}/${profileType}/${type}`
  try {
    const { data, error } = await supabaseAdmin.storage
      .from('media')
      .list(path)

    // If we get a 404, create an empty file to ensure the directory exists
    if (error && error.message.includes('404')) {
      const placeholderPath = `${path}/.placeholder`
      await supabaseAdmin.storage
        .from('media')
        .upload(placeholderPath, new Blob([''], { type: 'text/plain' }), {
          upsert: true
        })
    }
  } catch (error) {
    console.error('Error ensuring directory exists:', error)
  }
}

export async function uploadMedia({ userId, type, file, profileType }: UploadOptions) {
  // Validate file type
  const allowedTypes = ['image/jpeg', 'image/png', 'image/webp', 'image/gif', 'video/webm'];
  if (!allowedTypes.includes(file.type)) {
    throw new Error(`Invalid file type. Allowed types: ${allowedTypes.join(', ')}`);
  }

  // Validate file size (8MB)
  const MAX_SIZE = 8 * 1024 * 1024;
  if (file.size > MAX_SIZE) {
    throw new Error('File too large. Maximum size is 8MB');
  }

  try {
    // Ensure the directory exists before we try to upload
    await ensureDirectoryExists(userId, type, profileType)
    
    // Delete old files before uploading new one
    await deleteOldFiles(userId, type, profileType)

    const fileExt = file.name.split('.').pop()
    const fileName = `${crypto.randomUUID()}.${fileExt}`
    const filePath = `users/${userId}/${profileType}/${type}/${fileName}`

    // Upload the new file
    const { error: uploadError, data } = await supabaseAdmin.storage
      .from('media')
      .upload(filePath, file, {
        cacheControl: '3600',
        upsert: true,
        contentType: file.type
      })

    if (uploadError) {
      console.error('Upload error:', uploadError)
      throw uploadError
    }

    // Get the public URL with a timestamp to prevent caching
    const timestamp = Date.now()
    const { data: { publicUrl } } = supabaseAdmin.storage
      .from('media')
      .getPublicUrl(`${filePath}?t=${timestamp}`)

    return {
      url: publicUrl,
      metadata: {
        originalName: file.name,
        mimeType: file.type,
        size: file.size,
        path: filePath,
        userId,
        type,
        profileType
      }
    }
  } catch (error) {
    console.error('Supabase error:', error)
    throw error
  }
} 