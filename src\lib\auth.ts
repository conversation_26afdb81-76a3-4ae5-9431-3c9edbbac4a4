import { AuthOptions } from 'next-auth'
import Credentials<PERSON>rovider from 'next-auth/providers/credentials'
import { SiweMessage } from 'siwe'
import { User } from '@/models/User'
import connectDB from '@/lib/mongodb/mongoose'

export const authOptions: AuthOptions = {
  providers: [
    CredentialsProvider({
      id: 'web3',
      name: 'Web3',
      credentials: {
        message: { label: 'Message', type: 'text' },
        signature: { label: 'Signature', type: 'text' },
        username: { label: 'Username', type: 'text' },
        walletType: { label: 'Wallet Type', type: 'text' },
      },
      async authorize(credentials, req) {
        try {
          if (!credentials?.message || !credentials?.signature || !credentials?.username || !credentials?.walletType) {
            throw new Error('Missing required credentials');
          }

          let walletAddress;
          const walletType = credentials.walletType;

          // Handle different wallet types
          switch (walletType) {
            case 'ethereum':
              const siwe = new SiweMessage(JSON.parse(credentials.message));
              const result = await siwe.verify({
                signature: credentials.signature,
              });

              if (!result.success) throw new Error('Invalid signature');
              // Convert to checksum address format
              walletAddress = siwe.address;
              break;

            case 'solana':
              // For Solana, we use the address directly - PRESERVE CASE
              walletAddress = credentials.message;
              break;

            case 'bitcoin':
              // For Bitcoin, we use the account address directly since WalletConnect handles verification
              walletAddress = credentials.message.toLowerCase();
              break;

            default:
              throw new Error('Unsupported wallet type');
          }

          await connectDB();
          
          // First check if username exists
          const existingUserWithUsername = await User.findOne({ 
            username: credentials.username.toLowerCase() 
          });

          // If username exists, verify wallet ownership
          if (existingUserWithUsername) {
            // For Solana, use exact case match. For others, use case-insensitive.
            const walletMatches = existingUserWithUsername.walletType === 'solana' 
              ? existingUserWithUsername.walletAddress === walletAddress
              : existingUserWithUsername.walletAddress.toLowerCase() === walletAddress.toLowerCase();

            if (walletMatches) {
              // Username exists and wallet matches - allow login
              return {
                id: existingUserWithUsername.id,
                walletAddress: existingUserWithUsername.walletAddress,
                walletType: existingUserWithUsername.walletType,
                username: existingUserWithUsername.username
              };
            } else {
              // Username exists but different wallet
              throw new Error('Username is already taken');
            }
          }

          // Username doesn't exist - check if wallet has a different username
          // For Solana addresses, use exact match
          const walletQuery = walletType === 'solana'
            ? { walletAddress: walletAddress }
            : { walletAddress: { $regex: new RegExp(`^${walletAddress}$`, 'i') } };

          const existingUserWithWallet = await User.findOne(walletQuery);
          if (existingUserWithWallet) {
            throw new Error('This wallet is already registered with a different username');
          }

          // Create new user
          const user = await User.create({
            walletAddress,
            walletType,
            username: credentials.username.toLowerCase(),
            coinProfile: {
              biography: '',
              links: {},
              avatar: { url: '/images/default-avatar.svg', isDefault: true },
              banner: { url: '/images/default-banner.svg', isDefault: true }
            },
            personalProfile: {
              biography: '',
              links: {},
              avatar: { url: '/images/default-avatar.svg', isDefault: true },
              banner: { url: '/images/default-banner.svg', isDefault: true }
            }
          });

          return {
            id: user.id,
            walletAddress: user.walletAddress,
            walletType: user.walletType,
            username: user.username
          };
        } catch (error) {
          console.error('Authorization error:', error);
          throw error; // Re-throw to handle in NextAuth
        }
      }
    }),
  ],
  session: {
    strategy: 'jwt',
    maxAge: 30 * 24 * 60 * 60, // 30 days
  },
  callbacks: {
    async session({ session, token }) {
      if (token) {
        session.user.id = token.sub as string;
        session.user.walletAddress = token.walletAddress as string;
        session.user.walletType = token.walletType as string;
        session.user.username = token.username as string;
      }
      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.walletAddress = user.walletAddress;
        token.walletType = user.walletType;
        token.username = user.username;
      }
      return token;
    },
  },
  pages: {
    signIn: '/',
    error: '/',
  },
  cookies: {
    sessionToken: {
      name: `${process.env.NODE_ENV === 'production' ? '__Secure-' : ''}next-auth.session-token`,
      options: {
        httpOnly: true,
        sameSite: 'lax',
        path: '/',
        secure: process.env.NODE_ENV === 'production',
      },
    },
  },
  debug: process.env.NODE_ENV === 'development',
} 