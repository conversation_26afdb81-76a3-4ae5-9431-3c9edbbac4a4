'use client'

import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Button } from '@/components/ui/button'
import { formatAddress } from '@/lib/utils'
import { Loader2 } from 'lucide-react'
import { useDisconnect } from 'wagmi'
import { signOut } from 'next-auth/react'
import { Navbar } from "@/components/navbar"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const { data: session, status } = useSession()
  const router = useRouter()
  const { disconnect } = useDisconnect()

  if (status === 'loading') {
    return (
      <div className="flex min-h-screen items-center justify-center">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    )
  }

  if (!session?.user?.walletAddress) {
    router.push('/')
    return null
  }

  const handleDisconnect = async () => {
    try {
      await disconnect()
      await signOut({ redirect: false })
      router.push('/')
    } catch (error) {
      console.error('Error disconnecting:', error)
    }
  }

  return (
    <div className="min-h-screen">
      <Navbar showConnectButton={false} />
      {children}
    </div>
  )
} 