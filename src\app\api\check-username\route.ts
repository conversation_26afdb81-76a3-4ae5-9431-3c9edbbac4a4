import { NextResponse } from 'next/server'
import connectDB from '@/lib/mongodb/mongoose'
import { User } from '@/models/User'

export async function POST(req: Request) {
  try {
    const { username } = await req.json()

    if (!username) {
      return NextResponse.json(
        { message: 'Username is required' },
        { status: 400 }
      )
    }

    // Validate username format
    const usernameRegex = /^[a-zA-Z0-9_-]{3,16}$/
    if (!usernameRegex.test(username)) {
      return NextResponse.json(
        { message: 'Invalid username format' },
        { status: 400 }
      )
    }

    await connectDB()

    // Check if username exists
    const existingUser = await User.findOne({ username: username.toLowerCase() })

    return NextResponse.json({
      exists: !!existingUser,
      message: existingUser ? 'Username is taken' : 'Username is available'
    })
  } catch (error) {
    console.error('Error checking username:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
} 