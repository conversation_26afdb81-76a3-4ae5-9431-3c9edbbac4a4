import mongoose from 'mongoose';

const shareLinkSchema = new mongoose.Schema({
  id: { type: String, required: true },
  createdAt: { type: Date, required: true },
  expiresAt: { type: Date, default: null }
});

const profileSchema = new mongoose.Schema({
  biography: {
    type: String,
    default: ''
  },
  avatar: {
    url: {
      type: String,
      default: '/images/default-avatar.svg'
    },
    isDefault: {
      type: Boolean,
      default: true
    },
    metadata: {
      type: {
        originalName: String,
        mimeType: String,
        size: Number
      },
      default: null
    }
  },
  banner: {
    url: {
      type: String,
      default: '/images/default-banner.svg'
    },
    isDefault: {
      type: Boolean,
      default: true
    },
    metadata: {
      type: {
        originalName: String,
        mimeType: String,
        size: Number
      },
      default: null
    }
  },
  links: {
    type: Map,
    of: String,
    default: {}
  }
}, { _id: false });

const coinProfileSchema = profileSchema.clone();
coinProfileSchema.add({
  contractAddress: {
    type: String,
    default: ''
  },
  links: {
    pumpfun: {
      type: String,
      default: ''
    },
    dexscreener: {
      type: String,
      default: ''
    },
    twitter: {
      type: String,
      default: ''
    },
    telegram: {
      type: String,
      default: ''
    },
    solscan: {
      type: String,
      default: ''
    },
    farcaster: {
      type: String,
      default: ''
    }
  }
});

const personalProfileSchema = profileSchema.clone();
personalProfileSchema.add({
  links: {
    twitter: {
      type: String,
      default: ''
    },
    instagram: {
      type: String,
      default: ''
    },
    youtube: {
      type: String,
      default: ''
    },
    facebook: {
      type: String,
      default: ''
    }
  }
});

const userSchema = new mongoose.Schema({
  walletAddress: {
    type: String,
    required: true,
    unique: true
  },
  walletType: {
    type: String,
    enum: ['ethereum', 'solana', 'bitcoin'],
    default: 'ethereum'
  },
  username: {
    type: String,
    required: true,
    unique: true,
    minlength: 3,
    maxlength: 16,
    match: /^[a-zA-Z0-9_-]+$/
  },
  isPublic: {
    type: Boolean,
    default: false
  },
  coinProfile: {
    type: coinProfileSchema,
    default: () => ({})
  },
  personalProfile: {
    type: personalProfileSchema,
    default: () => ({})
  },
  shareLinks: {
    type: [shareLinkSchema],
    default: []
  },
  createdAt: {
    type: Date,
    default: Date.now
  },
  updatedAt: {
    type: Date,
    default: Date.now
  }
}, { timestamps: true });

// Update the updatedAt field before saving
userSchema.pre('save', function(next) {
  this.updatedAt = new Date()
  next()
})

// Only create the model if it hasn't been created already
export const User = mongoose.models.User || mongoose.model('User', userSchema);

export type UserType = mongoose.InferSchemaType<typeof userSchema>;
export interface ProfileType {
  biography: string
  contractAddress?: string
  links: Map<string, string>
  type: 'coin' | 'personal'
  avatar?: {
    url: string
    isDefault: boolean
    metadata: {
      originalName?: string
      mimeType?: string
      size?: number
    }
  }
  banner?: {
    url: string
    isDefault: boolean
    metadata: {
      originalName?: string
      mimeType?: string
      size?: number
    }
  }
}