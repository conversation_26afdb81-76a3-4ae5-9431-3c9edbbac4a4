'use client'

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Wallet, DollarSign } from 'lucide-react'
import { TipJarModal } from './tip-jar-modal'
import { cn } from '@/lib/utils'

// Helper function to detect wallet type from address
function detectWalletType(address: string): 'ethereum' | 'solana' {
  // Solana addresses are 44 characters long and don't start with 0x
  if (address && address.length === 44 && !address.startsWith('0x')) {
    return 'solana'
  } 
  // Ethereum addresses are 42 characters long (including 0x) and start with 0x
  else if (address && address.length === 42 && address.startsWith('0x')) {
    return 'ethereum'
  }
  // Default to ethereum if we can't determine
  return 'ethereum'
}

interface TipButtonProps {
  recipientAddress: string
  walletType?: string
  username: string
  variant?: 'default' | 'primary' | 'secondary' | 'outline' | 'ghost'
  size?: 'default' | 'sm' | 'lg' | 'xl'
  className?: string
}

export function TipButton({
  recipientAddress,
  walletType,
  username,
  variant = 'default',
  size = 'default',
  className
}: TipButtonProps) {
  const [tipModalOpen, setTipModalOpen] = useState(false)
  
  // Detect wallet type based on address format
  const detectedWalletType = detectWalletType(recipientAddress)
  
  // Different gradient colors based on wallet type
  const gradientClass = detectedWalletType === 'ethereum'
    ? "from-accent/70 to-accent" // Ethereum colors
    : "from-accent/70 to-accent"; // Solana colors

  return (
    <>
      <Button
        variant={variant}
        size={size}
        onClick={() => setTipModalOpen(true)}
        className={cn(
          "relative overflow-hidden group transition-all duration-300",
          variant === 'primary' && "shadow-lg hover:shadow-accent/30",
          className
        )}
      >
        <span className={cn(
          "absolute inset-0 w-full h-full bg-gradient-to-r transform -translate-x-full group-hover:translate-x-0 transition-transform duration-300",
          gradientClass
        )}></span>
        <DollarSign className="w-5 h-5 mr-2 relative z-10 group-hover:text-white transition-colors" />
        <span className="relative z-10 group-hover:text-white transition-colors">
          Tip with {detectedWalletType === 'ethereum' ? 'ETH' : 'SOL'}
        </span>
      </Button>

      <TipJarModal
        open={tipModalOpen}
        onOpenChange={setTipModalOpen}
        recipientAddress={recipientAddress}
        walletType={detectedWalletType}
        username={username}
      />
    </>
  )
} 