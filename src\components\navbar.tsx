import Link from "next/link"
import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { useSession } from "next-auth/react"
import { useDisconnect } from "wagmi"
import { signOut } from "next-auth/react"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { Menu } from "lucide-react"

interface NavbarProps {
  onConnectWallet?: () => void
  isConnected?: boolean
  showConnectButton?: boolean
}

export function Navbar({ onConnectWallet, isConnected, showConnectButton = true }: NavbarProps) {
  const { data: session } = useSession()
  const { disconnect } = useDisconnect()
  const router = useRouter()
  const [isMenuOpen, setIsMenuOpen] = useState(false)

  const handleDisconnect = async () => {
    try {
      await disconnect()
      await signOut({ redirect: false })
      router.push('/')
    } catch (error) {
      console.error('Error disconnecting:', error)
    }
  }

  return (
    <header className="fixed top-0 left-0 right-0 z-50 border-b border-white/10 bg-background backdrop-blur-sm">
      <nav className="container mx-auto px-4 h-16 flex items-center justify-between">
        <div className="flex items-center space-x-8">
          <Link href="/" className="h-8 w-auto">
            <Image
              src="/images/coinlink-pixel-logo.png"
              alt="CoinLink"
              width={120}
              height={32}
              className="h-8 w-auto"
              priority
            />
          </Link>
          <div className="hidden md:flex space-x-6">
            <Link href="features" className="text-sm text-foreground/80 hover:text-foreground">
              Features
            </Link>
            <Link href="#about" className="text-sm text-foreground/80 hover:text-foreground">
              About
            </Link>
          </div>
        </div>
        <div className="flex items-center gap-4">
          {session?.user && (
            <Button 
              variant="default" 
              onClick={handleDisconnect}
            >
              Disconnect Wallet
            </Button>
          )}
          {showConnectButton && !session?.user && (
            <Button 
              variant="default" 
              onClick={onConnectWallet}
              disabled={isConnected}
            >
              {isConnected ? 'Connected' : 'Connect Wallet'}
            </Button>
          )}
          <button
            className="md:hidden p-2 rounded-md hover:bg-white/10"
            onClick={() => setIsMenuOpen(!isMenuOpen)}
          >
            <Menu className="w-6 h-6" />
          </button>
        </div>
      </nav>

      {/* Mobile Menu */}
      <div className={`md:hidden ${isMenuOpen ? 'block' : 'hidden'} absolute top-16 left-0 right-0 bg-background/95 backdrop-blur-sm border-b border-white/10`}>
        <div className="container mx-auto px-4 py-4 space-y-4">
          <Link 
            href="features" 
            className="block text-sm text-foreground/80 hover:text-foreground py-2"
            onClick={() => setIsMenuOpen(false)}
          >
            Features
          </Link>
          <Link 
            href="#about" 
            className="block text-sm text-foreground/80 hover:text-foreground py-2"
            onClick={() => setIsMenuOpen(false)}
          >
            About
          </Link>
        </div>
      </div>
    </header>
  )
} 