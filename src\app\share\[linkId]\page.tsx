import { User } from '@/models/User'
import connectDB from '@/lib/mongodb/mongoose'
import { notFound } from 'next/navigation'
import { TabsWrapper } from './TabsWrapper'
import { Footer } from "@/components/footer"

export const dynamic = 'force-dynamic'
export const revalidate = 0

interface SharePageProps {
  params: { linkId: string }
}

export default async function SharePage({ params }: SharePageProps) {
  await connectDB()
  
  // Find user by share link ID
  const user = await User.findOne({ 'shareLinks.id': params.linkId }).lean()

  if (!user) {
    notFound()
  }

  // Get the share link
  const shareLink = user.shareLinks.find((link: any) => link.id === params.linkId)
  
  // Check if link exists and is not expired
  if (!shareLink || (shareLink.expiresAt && new Date(shareLink.expiresAt) < new Date())) {
    // If link has expired, mark it as expired
    await User.updateOne(
      { _id: user._id },
      { $pull: { shareLinks: { id: params.linkId } } }
    )
    notFound()
  }

  // Create a clean user object with only the needed properties
  const cleanUser = {
    username: user.username,
    walletAddress: user.walletAddress,
    coinProfile: user.coinProfile ? {
      ...user.coinProfile,
      avatar: user.coinProfile.avatar,
      banner: user.coinProfile.banner,
    } : null,
    personalProfile: user.personalProfile ? {
      ...user.personalProfile,
      avatar: user.personalProfile.avatar,
      banner: user.personalProfile.banner,
    } : null,
  }

  const { coinProfile, personalProfile } = cleanUser
  const hasCoinProfile = coinProfile && (coinProfile.biography || Object.keys(coinProfile.links || {}).length > 0)
  const hasPersonalProfile = personalProfile && (personalProfile.biography || Object.keys(personalProfile.links || {}).length > 0)
  const defaultTab = hasCoinProfile ? 'coin' : 'personal'

  return (
    <div>
      <main className="min-h-screen bg-gradient-to-b from-background to-background/80">
        <TabsWrapper 
          defaultTab={defaultTab}
          hasCoinProfile={hasCoinProfile}
          hasPersonalProfile={hasPersonalProfile}
          coinProfile={coinProfile}
          personalProfile={personalProfile}
          walletAddress={cleanUser.walletAddress}
          username={cleanUser.username}
        />
      </main>
      <Footer />
    </div>
  )
} 