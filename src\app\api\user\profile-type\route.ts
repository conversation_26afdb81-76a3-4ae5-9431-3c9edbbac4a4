import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import connectDB from '@/lib/mongodb/mongoose'
import { User } from '@/models/User'

const PRESET_COIN_LINKS = [
  {
    title: 'PumpFun',
    url: '',
    icon: 'rocket',
    isActive: true,
    order: 0,
  },
  {
    title: 'Twitter',
    url: '',
    icon: 'twitter',
    isActive: true,
    order: 1,
  },
  {
    title: 'Telegram',
    url: '',
    icon: 'send',
    isActive: true,
    order: 2,
  },
  {
    title: 'Website',
    url: '',
    icon: 'globe',
    isActive: true,
    order: 3,
  },
  {
    title: 'Chart',
    url: '',
    icon: 'chart',
    isActive: true,
    order: 4,
  },
  {
    title: 'Block Explorer',
    url: '',
    icon: 'search',
    isActive: true,
    order: 5,
  },
  {
    title: 'Github',
    url: '',
    icon: 'github',
    isActive: true,
    order: 6,
  },
  {
    title: 'Farcaster',
    url: '',
    icon: 'radio',
    isActive: true,
    order: 7,
  },
]

const PRESET_CREATOR_LINKS = [
  {
    title: 'Twitter',
    url: '',
    icon: 'twitter',
    isActive: true,
    order: 0,
  },
  {
    title: 'Trading Bot',
    url: '',
    icon: 'crosshair',
    isActive: true,
    order: 1,
  },
  {
    title: 'Telegram',
    url: '',
    icon: 'send',
    isActive: true,
    order: 2,
  },
  {
    title: 'Website',
    url: '',
    icon: 'globe',
    isActive: true,
    order: 3,
  },
  {
    title: 'Github',
    url: '',
    icon: 'github',
    isActive: true,
    order: 4,
  },
  {
    title: 'Instagram',
    url: '',
    icon: 'instagram',
    isActive: true,
    order: 5,
  },
  {
    title: 'YouTube',
    url: '',
    icon: 'youtube',
    isActive: true,
    order: 6,
  },
  {
    title: 'Facebook',
    url: '',
    icon: 'facebook',
    isActive: true,
    order: 7,
  },
  {
    title: 'LinkedIn',
    url: '',
    icon: 'linkedin',
    isActive: true,
    order: 8,
  },
  {
    title: 'Farcaster',
    url: '',
    icon: 'radio',
    isActive: true,
    order: 9,
  },
]

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.walletAddress) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const { profileType } = await req.json()
    if (!profileType || !['coin', 'creator'].includes(profileType)) {
      return new NextResponse('Invalid profile type', { status: 400 })
    }

    await connectDB()
    const user = await User.findOne({ walletAddress: session.user.walletAddress })

    if (!user) {
      return new NextResponse('User not found', { status: 404 })
    }

    // Update profile type and set default links
    user.profileType = profileType
    user.links = profileType === 'coin' ? PRESET_COIN_LINKS : PRESET_CREATOR_LINKS

    await user.save()

    return NextResponse.json(user)
  } catch (error: any) {
    console.error('Error setting profile type:', error)
    return new NextResponse(error.message, { status: 500 })
  }
} 