Phantom Deeplinks
As of Phantom v22.04.11, iOS and Android apps can now natively interact with <PERSON> through either universal links (recommended) or deeplinks. We refer to both of these workflows collectively as "deeplinks".

Currently only Solana is supported for deeplinks.

All Provider Methods follow a protocol format of:

Copy
https://phantom.app/ul/<version>/<method>
It is also possible (but not recommended) to call these methods using Phantom's custom protocol handler:

Copy
phantom://<version>/<method>
In addition to these provider methods, Phantom also supports Other Methods that are accessible via deeplinks. Specifically, users can open web apps within <PERSON>'s in-app browser via the Browse deeplink.

In order to start interacting with <PERSON>, an app must first establish a connection. This connection request will prompt the user for permission to share their public key, indicating that they are willing to interact further.

Once a user connects to Phantom, <PERSON> will return a session param that should be used on all subsequent methods. For more information on sessions, please review Handling Sessions.

Base URL
Copy
https://phantom.app/ul/v1/connect
Query String Parameters
app_url (required): A url used to fetch app metadata (i.e. title, icon) using the same properties found in Displaying Your App. URL-encoded.

dapp_encryption_public_key (required): A public key used for end-to-end encryption. This will be used to generate a shared secret. For more information on how Phantom handles shared secrets, please review Encryption. 

redirect_link (required): The URI where Phantom should redirect the user upon connection. Please review Specifying Redirects for more details. URL-encoded.

cluster (optional): The network that should be used for subsequent interactions. Can be either: mainnet-beta, testnet, or devnet. Defaults to mainnet-beta.

Returns
✅ Approve
phantom_encryption_public_key: An encryption public key used by Phantom for the construction of a shared secret between the connecting app and Phantom, encoded in base58.

nonce: A nonce used for encrypting the response, encoded in base58.

data: An encrypted JSON string. Refer to Encryption to learn how apps can decrypt data using a shared secret. Encrypted bytes are encoded in base58.

Copy
// content of decrypted `data`-parameter
{
  // base58 encoding of user public key
  "public_key": "BSFtCudCd4pR4LSFqWPjbtXPKSNVbGkc35gRNdnqjMCU",

  // session token for subsequent signatures and messages
  // dapps should send this with any other deeplinks after connect
  "session": "..."
}
public_key: The public key of the user, represented as a base58-encoded string.

session: A string encoded in base58. This should be treated as opaque by the connecting app, as it only needs to be passed alongside other parameters. Sessions do not expire. For more information on sessions, please review Handling Sessions.

❌Reject
An errorCode and errorMessage as query parameters. Please refer to Errors for a full list of possible error codes.

Copy
{
  "errorCode": "...",
  "errorMessage": "..."
} import "react-native-get-random-values";
import "react-native-url-polyfill/auto";
import { Buffer } from "buffer";
global.Buffer = global.Buffer || Buffer;
import React, { useCallback, useEffect, useRef, useState } from "react";
import { Button, Platform, ScrollView, StyleSheet, Text, View } from "react-native";
import { StatusBar } from "expo-status-bar";
import * as Linking from "expo-linking";
import nacl from "tweetnacl";
import bs58 from "bs58";
import {
  clusterApiUrl,
  Connection,
  Keypair,
  PublicKey,
  SystemProgram,
  Transaction,
} from "@solana/web3.js";

const NETWORK = clusterApiUrl("mainnet-beta");

const onConnectRedirectLink = Linking.createURL("onConnect");
const onDisconnectRedirectLink = Linking.createURL("onDisconnect");
const onSignAndSendTransactionRedirectLink = Linking.createURL("onSignAndSendTransaction");
const onSignAllTransactionsRedirectLink = Linking.createURL("onSignAllTransactions");
const onSignTransactionRedirectLink = Linking.createURL("onSignTransaction");
const onSignMessageRedirectLink = Linking.createURL("onSignMessage");

const buildUrl = (path: string, params: URLSearchParams) =>
  `https://phantom.app/ul/v1/${path}?${params.toString()}`;

const decryptPayload = (data: string, nonce: string, sharedSecret?: Uint8Array) => {
  if (!sharedSecret) throw new Error("missing shared secret");

  const decryptedData = nacl.box.open.after(bs58.decode(data), bs58.decode(nonce), sharedSecret);
  if (!decryptedData) {
    throw new Error("Unable to decrypt data");
  }
  return JSON.parse(Buffer.from(decryptedData).toString("utf8"));
};

const encryptPayload = (payload: any, sharedSecret?: Uint8Array) => {
  if (!sharedSecret) throw new Error("missing shared secret");

  const nonce = nacl.randomBytes(24);

  const encryptedPayload = nacl.box.after(
    Buffer.from(JSON.stringify(payload)),
    nonce,
    sharedSecret
  );

  return [nonce, encryptedPayload];
};

export default function App() {
  const [deepLink, setDeepLink] = useState<string>("");
  const [logs, setLogs] = useState<string[]>([]);
  const connection = new Connection(NETWORK);
  const addLog = useCallback((log: string) => setLogs((logs) => [...logs, "> " + log]), []);
  const scrollViewRef = useRef<any>(null);

  // store dappKeyPair, sharedSecret, session and account SECURELY on device
  // to avoid having to reconnect users.
  const [dappKeyPair] = useState(nacl.box.keyPair());
  const [sharedSecret, setSharedSecret] = useState<Uint8Array>();
  const [session, setSession] = useState<string>();
  const [phantomWalletPublicKey, setPhantomWalletPublicKey] = useState<PublicKey>();

  useEffect(() => {
    (async () => {
      const initialUrl = await Linking.getInitialURL();
      if (initialUrl) {
        setDeepLink(initialUrl);
      }
    })();
    Linking.addEventListener("url", handleDeepLink);
    return () => {
      Linking.removeEventListener("url", handleDeepLink);
    };
  }, []);

  const handleDeepLink = ({ url }: Linking.EventType) => {
    setDeepLink(url);
  };

  // handle inbounds links
  useEffect(() => {
    if (!deepLink) return;

    const url = new URL(deepLink);
    const params = url.searchParams;

    if (params.get("errorCode")) {
      addLog(JSON.stringify(Object.fromEntries([...params]), null, 2));
      return;
    }

    if (/onConnect/.test(url.pathname)) {
      const sharedSecretDapp = nacl.box.before(
        bs58.decode(params.get("phantom_encryption_public_key")!),
        dappKeyPair.secretKey
      );

      const connectData = decryptPayload(
        params.get("data")!,
        params.get("nonce")!,
        sharedSecretDapp
      );

      setSharedSecret(sharedSecretDapp);
      setSession(connectData.session);
      setPhantomWalletPublicKey(new PublicKey(connectData.public_key));

      addLog(JSON.stringify(connectData, null, 2));
    } else if (/onDisconnect/.test(url.pathname)) {
      addLog("Disconnected!");
    } else if (/onSignAndSendTransaction/.test(url.pathname)) {
      const signAndSendTransactionData = decryptPayload(
        params.get("data")!,
        params.get("nonce")!,
        sharedSecret
      );

      addLog(JSON.stringify(signAndSendTransactionData, null, 2));
    } else if (/onSignAllTransactions/.test(url.pathname)) {
      const signAllTransactionsData = decryptPayload(
        params.get("data")!,
        params.get("nonce")!,
        sharedSecret
      );

      const decodedTransactions = signAllTransactionsData.transactions.map((t: string) =>
        Transaction.from(bs58.decode(t))
      );

      addLog(JSON.stringify(decodedTransactions, null, 2));
    } else if (/onSignTransaction/.test(url.pathname)) {
      const signTransactionData = decryptPayload(
        params.get("data")!,
        params.get("nonce")!,
        sharedSecret
      );

      const decodedTransaction = Transaction.from(bs58.decode(signTransactionData.transaction));

      addLog(JSON.stringify(decodedTransaction, null, 2));
    } else if (/onSignMessage/.test(url.pathname)) {
      const signMessageData = decryptPayload(
        params.get("data")!,
        params.get("nonce")!,
        sharedSecret
      );

      addLog(JSON.stringify(signMessageData, null, 2));
    }
  }, [deepLink]);

  const createTransferTransaction = async () => {
    if (!phantomWalletPublicKey) throw new Error("missing public key from user");
    let transaction = new Transaction().add(
      SystemProgram.transfer({
        fromPubkey: phantomWalletPublicKey,
        toPubkey: phantomWalletPublicKey,
        lamports: 100,
      })
    );
    transaction.feePayer = phantomWalletPublicKey;
    addLog("Getting recent blockhash");
    const anyTransaction: any = transaction;
    anyTransaction.recentBlockhash = (await connection.getLatestBlockhash()).blockhash;
    return transaction;
  };

  const connect = async () => {
    const params = new URLSearchParams({
      dapp_encryption_public_key: bs58.encode(dappKeyPair.publicKey),
      cluster: "mainnet-beta",
      app_url: "https://phantom.app",
      redirect_link: onConnectRedirectLink,
    });

    const url = buildUrl("connect", params);
    Linking.openURL(url);
  };

  const disconnect = async () => {
    const payload = {
      session,
    };
    const [nonce, encryptedPayload] = encryptPayload(payload, sharedSecret);

    const params = new URLSearchParams({
      dapp_encryption_public_key: bs58.encode(dappKeyPair.publicKey),
      nonce: bs58.encode(nonce),
      redirect_link: onDisconnectRedirectLink,
      payload: bs58.encode(encryptedPayload),
    });

    const url = buildUrl("disconnect", params);
    Linking.openURL(url);
  };

  const signAndSendTransaction = async () => {
    const transaction = await createTransferTransaction();

    const serializedTransaction = transaction.serialize({
      requireAllSignatures: false,
    });

    const payload = {
      session,
      transaction: bs58.encode(serializedTransaction),
    };
    const [nonce, encryptedPayload] = encryptPayload(payload, sharedSecret);

    const params = new URLSearchParams({
      dapp_encryption_public_key: bs58.encode(dappKeyPair.publicKey),
      nonce: bs58.encode(nonce),
      redirect_link: onSignAndSendTransactionRedirectLink,
      payload: bs58.encode(encryptedPayload),
    });

    addLog("Sending transaction...");
    const url = buildUrl("signAndSendTransaction", params);
    Linking.openURL(url);
  };

  const signAllTransactions = async () => {
    const transactions = await Promise.all([
      createTransferTransaction(),
      createTransferTransaction(),
    ]);

    const serializedTransactions = transactions.map((t) =>
      bs58.encode(
        t.serialize({
          requireAllSignatures: false,
        })
      )
    );

    const payload = {
      session,
      transactions: serializedTransactions,
    };

    const [nonce, encryptedPayload] = encryptPayload(payload, sharedSecret);

    const params = new URLSearchParams({
      dapp_encryption_public_key: bs58.encode(dappKeyPair.publicKey),
      nonce: bs58.encode(nonce),
      redirect_link: onSignAllTransactionsRedirectLink,
      payload: bs58.encode(encryptedPayload),
    });

    addLog("Signing transactions...");
    const url = buildUrl("signAllTransactions", params);
    Linking.openURL(url);
  };

  const signTransaction = async () => {
    const transaction = await createTransferTransaction();

    const serializedTransaction = bs58.encode(
      transaction.serialize({
        requireAllSignatures: false,
      })
    );

    const payload = {
      session,
      transaction: serializedTransaction,
    };

    const [nonce, encryptedPayload] = encryptPayload(payload, sharedSecret);

    const params = new URLSearchParams({
      dapp_encryption_public_key: bs58.encode(dappKeyPair.publicKey),
      nonce: bs58.encode(nonce),
      redirect_link: onSignTransactionRedirectLink,
      payload: bs58.encode(encryptedPayload),
    });

    addLog("Signing transaction...");
    const url = buildUrl("signTransaction", params);
    Linking.openURL(url);
  };

  const signMessage = async () => {
    const message = "To avoid digital dognappers, sign below to authenticate with CryptoCorgis.";

    const payload = {
      session,
      message: bs58.encode(Buffer.from(message)),
    };

    const [nonce, encryptedPayload] = encryptPayload(payload, sharedSecret);

    const params = new URLSearchParams({
      dapp_encryption_public_key: bs58.encode(dappKeyPair.publicKey),
      nonce: bs58.encode(nonce),
      redirect_link: onSignMessageRedirectLink,
      payload: bs58.encode(encryptedPayload),
    });

    addLog("Signing message...");
    const url = buildUrl("signMessage", params);
    Linking.openURL(url);
  };

  return (
    <View style={{ flex: 1, backgroundColor: "#333" }}>
      <StatusBar style="light" />
      <View style={{ flex: 1 }}>
        <ScrollView
          contentContainerStyle={{
            backgroundColor: "#111",
            padding: 20,
            paddingTop: 100,
            flexGrow: 1,
          }}
          ref={scrollViewRef}
          onContentSizeChange={() => {
            scrollViewRef.current.scrollToEnd({ animated: true });
          }}
          style={{ flex: 1 }}
        >
          {logs.map((log, i) => (
            <Text
              key={`t-${i}`}
              style={{
                fontFamily: Platform.OS === "ios" ? "Courier New" : "monospace",
                color: "#fff",
                fontSize: 14,
              }}
            >
              {log}
            </Text>
          ))}
        </ScrollView>
      </View>
      <View style={{ flex: 0, paddingTop: 20, paddingBottom: 40 }}>
        <Btn title="Connect" onPress={connect} />
        <Btn title="Disconnect" onPress={disconnect} />
        <Btn title="Sign And Send Transaction" onPress={signAndSendTransaction} />
        <Btn title="Sign All Transactions" onPress={signAllTransactions} />
        <Btn title="Sign Transaction" onPress={signTransaction} />
        <Btn title="Sign Message" onPress={signMessage} />
      </View>
    </View>
  );
}

const Btn = ({ title, onPress }: { title: string; onPress: () => Promise<void> }) => {
  return (
    <View style={{ marginVertical: 10 }}>
      <Button title={title} onPress={onPress} />
    </View>
  );
};


Handling Sessions
When a user connects to Phantom for the first time, Phantom will return a session param that represents the user's connection. The app should pass this session param back to Phantom on all subsequent Provider Methods. It is the app's responsibility to store this session.

Sessions do not expire. Once a user has connected with Phantom, the corresponding app can indefinitely make requests such as SignAndSendTransaction and SignMessage without prompting the user to re-connect with Phantom. Apps will still need to re-connect to Phantom after a Disconnect event or an Invalid Session.

Session Structure
The entire session param is encoded in base58. A session should contain the following data:

JSON Data Signature: A base58 signature of the JSON data that is 64 bytes. Phantom will check the signature against the actual message that was signed.

JSON Data: A JSON object with the following fields:

app_url (string): A url used to fetch app metadata (i.e. title, icon) using the same properties found in Displaying Your App.

timestamp (number): The timestamp at which the user approved the connection. At the time of this writing, sessions do not expire.

chain (string): The chain that the user connected to at the start of the session. Sessions cannot be used across two different chains with the same keypair (e.g. the user cannot connect to Solana and then sign on Ethereum). At the time of this writing, Phantom only supports solana.

cluster (string) (optional): The approved cluster that the app and user initially connected to. Solana-only. Can be either: mainnet-beta, testnet, or devnet. Defaults to mainnet-beta.

Decoding Sessions
Phantom will decode and validate the session param on every request. To decode the session, we decode it with bs58, slice off the first 64 bytes of the signature, and the treat the rest as JSON data. We then sign the JSON data again with the same keypair and compare that signature against the signature in the session. If the signatures are the same, the session is valid. Otherwise, we conclude that the session has been faked, as the signature does not belong to the keypair it claims it does.

Calling nacl.sign.open conveniently verifies and returns the original object. For more information, please review Encryption Resources.

After we determine that the session is valid, we still need to ensure that the JSON fields line up with what we expect. An app could give a session for pubkey A when the user is currently using pubkey B in Phantom. In such a scenario, that session should not allow an app to request signatures. Instead, the app must issue a new connect request or use the correct session.

Copy
// Encoding a session
const privateKey = ...;
const sessionData = JSON.stringify({
  "app_id": "APP_ID",
  "chain": "CHAIN",
  "cluster": "CLUSTER",
  "timestamp": 1644954984,
});
const bytes = Buffer.from(sessionData, "utf-8");

// tweetnacl-js formats signature in format <signature><sessionData>
const signature = bs58.encode(nacl.sign(bytes, privateKey));

// Decoding ja session
const publicKey = ...;
const verifiedSessionData = nacl.sign.open(bs58.decode(signature), publicKey.toBytes());
if (!verifiedSessionData) throw new Error(`This session was not signed by ${publicKey}`);
Invalid Sessions
While sessions do not expire, there are a number of reasons why a sessions could still be deemed invalid:

It was not signed by the current wallet keypair. This could mean that the session is entirely fake, or that it was signed by another keypair in the user’s wallet.

It was signed by the current wallet keypair, but the session's JSON data does not pass muster. There are a few reasons why this might occur:

The user switched chains (or possibly networks).

The app_url could be blocked if malicious. See Blocklist for more information.



Specifying Redirects
All Methods support a redirect_link= param that lets Phantom know how to get back to the original app. The URI specified by this param should be URL encoded. The following is an example for a mydapp://onPhantomConnected redirect URI:

Copy
redirect_link%3Dmydapp%3A%2F%2FonPhantomConnected
If the deeplink request to Phantom comes with a response, Phantom will append the results as query parameters in the redirect_link= upon redirecting.

Copy
redirect_link=mydapp://onPhantomConnected?data=...

Encryption
Deeplinks are encrypted using symmetric key encryption generated from a Diffie-Hellman key exchange. While deeplink sessions will be created in plaintext, an encrypted channel will be created to prevent session tokens from getting hijacked.

Encryption & Decryption Workflow
Phantom deeplinks are encrypted with the following workflows:

Connect
[dapp]: On the initial connect deeplink, dapps should include a dapp_encryption_public_key query parameter. It's recommended to create a new x25519 keypair for every session started with connect. In all methods, the public key for this keypair is referred to as dapp_encryption_public_key.

[phantom]: Upon handling a connect deeplink, Phantom will also generate a new x25519 keypair.

Phantom will return this public key as phantom_encryption_public_key in the connect response.

Phantom will create a secret key using Diffie-Hellman with dapp_encryption_public_key and the private key associated with phantom_encryption_public_key.

Phantom will locally store a mapping of dapp_encryption_public_key to shared secrets for use with decryption in subsequent deeplinks.

[dapp]: Upon receiving the connect response, the dapp should create a shared secret by using Diffie-Hellman with phantom_encryption_public_key and the private key associated with dapp_encryption_public_key. This shared secret should then be used to decrypt the data field in the response. If done correctly, the user's public key will be available to share with the dapp inside the data JSON object.

Subsequent Deeplinks
[dapp]: For any subsequent methods (such as SignAndSendTransaction and SignMessage), dapps should send a dapp_encryption_public_key (the public key side of the shared secret) used with Phantom along with an encrypted payload object. 

[phantom]: Upon approval, Phantom will encrypt the signed response as a JSON object with the encryption sent as a data= query param.

[dapp]: Upon receiving the deeplink response, dapps should decrypt the object in the data= query param to view the signature.

Encryption Resources
To learn more about encryption and decryption, please refer to the following libraries:

JavaScript
TweetNaCl.js

iOS
TweetNaCl SwiftWrap