'use client'

import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON>eader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { useConnect, useSignMessage, useAccount, useDisconnect } from 'wagmi'
import { SiweMessage } from 'siwe'
import Image from 'next/image'
import { useState, useEffect } from 'react'
import { ChevronRight, ArrowLeft } from 'lucide-react'
import { signIn } from 'next-auth/react'
import toast from 'react-hot-toast'
import nacl from 'tweetnacl'
import bs58 from 'bs58'

interface WalletConnectModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  username?: string
}

type WalletType = 'ethereum' | 'solana' | 'bitcoin'

// Mobile deep links for wallets
const WALLET_LINKS = {
  metaMask: {
    android: 'https://metamask.app.link/dapp/',
    ios: 'https://metamask.app.link/dapp/',
    name: 'Meta<PERSON><PERSON>',
    downloadUrl: 'https://metamask.io/download/',
  },
  phantom: {
    android: 'phantom://',  // Direct protocol handler
    ios: 'phantom://', // Direct protocol handler
    name: 'Phantom',
    downloadUrl: 'https://phantom.app/',
  },
  coinbase: {
    android: 'https://go.cb-w.com/mtUDhEZPy1',
    ios: 'https://go.cb-w.com/mtUDhEZPy1',
    name: 'Coinbase Wallet',
    downloadUrl: 'https://www.coinbase.com/wallet/downloads',
  },
  bitcoin: {
    android: 'bitcoin://',
    ios: 'bitcoin://',
    name: 'Bitcoin Wallet',
    downloadUrl: 'https://bitcoin.org/en/choose-your-wallet',
  }
}

export function WalletConnectModal({ open, onOpenChange, username }: WalletConnectModalProps) {
  const { address } = useAccount()
  const { disconnect } = useDisconnect()
  const { signMessageAsync } = useSignMessage()
  const [isAuthenticating, setIsAuthenticating] = useState(false)
  const [showMore, setShowMore] = useState(false)
  const [isMobile, setIsMobile] = useState(false)
  const [hasPhantom, setHasPhantom] = useState(false)
  const [hasMetaMask, setHasMetaMask] = useState(false)

  // Detect mobile device
  useEffect(() => {
    setIsMobile(/iPhone|iPad|iPod|Android/i.test(navigator.userAgent))
  }, [])

  // Handle URL parameters for Phantom response
  useEffect(() => {
    if (!open) return;
    
    // Check for phantom callback in URL
    const searchParams = new URLSearchParams(window.location.search);
    const phantomEncryptionPublicKey = searchParams.get('phantom_encryption_public_key');
    const nonce = searchParams.get('nonce');
    const data = searchParams.get('data');
    const errorCode = searchParams.get('errorCode');
    const errorMessage = searchParams.get('errorMessage');

    if (errorCode || errorMessage) {
      console.error('Phantom connection error:', errorCode, errorMessage);
      toast.error(errorMessage || 'Failed to connect to Phantom wallet');
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
      return;
    }

    // Process successful connection
    if (phantomEncryptionPublicKey && nonce && data) {
      try {
        console.log('Received Phantom response:', { phantomEncryptionPublicKey, nonce, data });
        
        // Get stored keypair
        const storedKeyPair = localStorage.getItem('dappKeyPair');
        if (!storedKeyPair) {
          throw new Error('No stored keypair found');
        }
        
        const keypair = JSON.parse(storedKeyPair);
        const secretKey = bs58.decode(keypair.secretKey);
        
        // Create shared secret
        const sharedSecret = nacl.box.before(
          bs58.decode(phantomEncryptionPublicKey),
          secretKey
        );
        
        // Decrypt the data
        const decryptedData = decryptPayload(data, nonce, sharedSecret);
        console.log('Decrypted data:', decryptedData);
        
        if (decryptedData.public_key) {
          // Process the Solana wallet address
          const walletAddress = decryptedData.public_key;
          
          // Store session for later use
          localStorage.setItem('phantomSession', decryptedData.session);
          
          // Handle user authentication
          checkExistingUser(walletAddress);
        }
        
        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname);
      } catch (error) {
        console.error('Error processing Phantom response:', error);
        toast.error('Failed to process wallet connection');
      }
    }
  }, [open]);

  // Function to decrypt payload from Phantom
  const decryptPayload = (data: string, nonce: string, sharedSecret: Uint8Array) => {
    try {
      const decryptedData = nacl.box.open.after(
        bs58.decode(data),
        bs58.decode(nonce),
        sharedSecret
      );
      
      if (!decryptedData) {
        throw new Error('Unable to decrypt data');
      }
      
      return JSON.parse(Buffer.from(decryptedData).toString('utf8'));
    } catch (error) {
      console.error('Decryption error:', error);
      throw new Error('Failed to decrypt Phantom response');
    }
  };

  // Detect installed wallets and check for mobile connections
  useEffect(() => {
    const checkWallets = async () => {
      if (isMobile) {
        // On mobile, check if wallets are available and connected
        if (window.phantom?.solana) {
          try {
            // Check if Phantom is already connected
            const response = await window.phantom.solana.connect({ onlyIfTrusted: true });
            if (response?.publicKey) {
              await checkExistingUser(response.publicKey.toString());
              return;
            }
          } catch (error) {
            // Not connected, continue
          }
        }

        if (window.ethereum && window.ethereum.isMetaMask) {
          try {
            // Check if MetaMask is already connected
            const accounts = await window.ethereum.request({ method: 'eth_accounts' });
            if (accounts.length > 0) {
              await checkExistingUser(accounts[0]);
              return;
            }
          } catch (error) {
            // Not connected, continue
          }
        }

        setHasPhantom(false);
        setHasMetaMask(false);
      } else {
        // Desktop detection
        const hasPhantom = window?.phantom?.solana || false;
        const hasMetaMask = typeof window.ethereum !== 'undefined' && window.ethereum.isMetaMask;
        setHasPhantom(hasPhantom);
        setHasMetaMask(hasMetaMask);
      }
    };

    if (open) {
      checkWallets();
    }
  }, [open, isMobile]);

  // Persist username to localStorage when it changes
  useEffect(() => {
    if (username && username.trim() !== '') {
      localStorage.setItem('phantomUsername', username);
    }
  }, [username]);

  // Listen for Phantom callback event (for mobile global login)
  useEffect(() => {
    function handlePhantomCallback(event: any) {
      const payload = event.detail;
      if (payload && payload.public_key) {
        // Store session for later use
        localStorage.setItem('phantomSession', payload.session);
        // Trigger login flow
        checkExistingUser(payload.public_key);
      }
    }
    window.addEventListener('phantom-wallet-callback', handlePhantomCallback);
    return () => window.removeEventListener('phantom-wallet-callback', handlePhantomCallback);
  }, [open, username]);



  // Handle mobile wallet connection
  const handleMobileWallet = async (walletId: string) => {
    try {
      if (walletId === 'phantom') {
        // For Phantom, just show a message to manually open the app
        toast.success('Please open Phantom app and connect manually, then return to this page');
        // Close the modal so user can see the page
        onOpenChange(false);
      } else if (walletId === 'metaMask') {
        // For MetaMask, try the deep link but also show instructions
        const metaMaskUrl = `https://metamask.app.link/dapp/${window.location.host}`;
        window.location.href = metaMaskUrl;

        // Show instructions
        setTimeout(() => {
          toast.success('If MetaMask didn\'t open, please open it manually and navigate to this site');
        }, 2000);
      }
    } catch (error) {
      console.error('Error connecting to wallet:', error);
      toast.error('Failed to connect to wallet');
    }
  };

  // Use stored username if not provided
  const getEffectiveUsername = () => {
    return username && username.trim() !== '' ? username : localStorage.getItem('phantomUsername') || '';
  };

  const checkExistingUser = async (walletAddress: string) => {
    try {
      // Use effective username (from prop or localStorage)
      const effectiveUsername = getEffectiveUsername();
      if (!effectiveUsername || effectiveUsername.trim() === '') {
        toast.error('Please enter a username')
        onOpenChange(false)
        disconnect()
        return
      }

      // Determine wallet type based on address format
      const walletType = walletAddress.length === 44 ? 'solana' : 'ethereum'
      
      const usernameResponse = await fetch('/api/check-username', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ username: effectiveUsername.toLowerCase() }),
      })

      const usernameData = await usernameResponse.json()

      if (usernameResponse.ok) {
        if (!usernameData.exists) {
          // Username doesn't exist - proceed with registration
          console.log('Username available, registering new user')
          await handleAuthentication(walletAddress)
          return
        }

        // Username exists - check if wallet matches
        const walletResponse = await fetch('/api/check-wallet', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ 
            username: effectiveUsername.toLowerCase(), 
            walletAddress: walletType === 'solana' ? walletAddress : walletAddress.toLowerCase(),
            walletType
          }),
        })

        const walletData = await walletResponse.json()

        if (!walletResponse.ok) {
          throw new Error('Failed to verify wallet ownership')
        }

        if (walletData.isOwner) {
          // Wallet matches username - proceed with sign in
          console.log('Wallet verified, signing in')
          await handleAuthentication(walletAddress, effectiveUsername)
        } else {
          // Username exists but different wallet
          toast.error('This username is registered to a different wallet address')
          onOpenChange(false)
          disconnect()
        }
      } else {
        throw new Error('Failed to check username availability')
      }
    } catch (error: any) {
      console.error('Error checking user:', error)
      toast.error(error.message || 'Failed to verify user')
      onOpenChange(false)
      disconnect()
    }
  }

  const handleAuthentication = async (walletAddress: string, existingUsername?: string) => {
    try {
      setIsAuthenticating(true)
      // Use effective username (from prop, argument, or localStorage)
      const finalUsername = existingUsername || getEffectiveUsername();

      // Get nonce
      const nonceResponse = await fetch('/api/auth/nonce')
      if (!nonceResponse.ok) {
        throw new Error('Failed to get authentication nonce')
      }
      const nonce = await nonceResponse.text()
      
      // Determine wallet type based on address format
      let walletType = 'ethereum'
      if (walletAddress.length === 44) { // Solana address length
        walletType = 'solana'
      }
      
      let message, signature
      
      if (walletType === 'ethereum') {
        // Create SIWE message for Ethereum
        message = new SiweMessage({
          domain: window.location.host,
          address: walletAddress,
          statement: 'Sign in with Ethereum to Wallet Tree',
          uri: window.location.origin,
          version: '1',
          chainId: 1,
          nonce,
        })
        
        const messageToSign = message.prepareMessage()
        signature = await signMessageAsync({ message: messageToSign })
      } else {
        // For Solana, we'll use the wallet address directly
        message = walletAddress
        signature = nonce // We're not actually using the signature for Solana
      }
      
      const callbackUrl = process.env.NEXTAUTH_URL || window.location.origin;
      const result = await signIn('web3', {
        message: walletType === 'ethereum' ? JSON.stringify(message) : message,
        signature,
        username: finalUsername,
        walletType,
        redirect: false,
        callbackUrl: `${callbackUrl}/dashboard`,
      })
      
      if (result?.error) {
        throw new Error(result.error)
      }
      
      // Close modal and redirect to dashboard
      onOpenChange(false)
      toast.success(existingUsername ? 'Successfully logged in' : 'Successfully registered')
      
      // Use the result URL if available, otherwise fallback to dashboard
      if (result?.url) {
        window.location.href = result.url
      } else {
        window.location.href = '/dashboard'
      }
      // Clear stored username after successful login
      localStorage.removeItem('phantomUsername');
    } catch (error: any) {
      console.error('Authentication error:', error)
      toast.error(error.message || 'Failed to authenticate')
      disconnect()
    } finally {
      setIsAuthenticating(false)
    }
  }

  const { connect, connectors, isLoading, pendingConnector } = useConnect({
    onSuccess: async (data) => {
      const walletAddress = data.account
      
      try {
        await checkExistingUser(walletAddress)
      } catch (error: any) {
        console.error('Connection error:', error)
        toast.error(error.message || 'Failed to connect wallet')
        disconnect()
        onOpenChange(false)
      }
    },
    onError: (error) => {
      console.error('Connection error:', error)
      toast.error('Failed to connect wallet')
      disconnect()
      onOpenChange(false)
    }
  })

  // Popular wallets to show in the first view
  const popularWallets = ['metaMask', 'phantom-solana']
  const popularConnectors = connectors.filter(c => {
    if (c.id === 'phantom') {
      // Only create Solana connector for Phantom
      return true
    }
    return popularWallets.includes(c.id)
  }).flatMap(c => {
    if (c.id === 'phantom') {
      // Only create Solana option for Phantom
      return [
        {
          ...c,
          virtualId: 'phantom-solana',
          name: 'Phantom (Solana)',
          isSolana: true
        }
      ]
    }
    return [c]
  })

  // Additional wallets for the "More options" view
  const walletCategories = {
    'Ethereum': ['metaMask', 'walletConnect'],
    'Solana': ['phantom-solana'],
    'Bitcoin': ['bitcoin'],
    'Browser': ['injected'],
  }

  const handleConnect = async (connector: any) => {
    try {
      if (isMobile) {
        // For mobile, always use deep links
        if (connector.virtualId === 'phantom-solana' || connector.id === 'phantom') {
          handleMobileWallet('phantom')
          return
        }
        if (connector.id === 'metaMask') {
          handleMobileWallet('metaMask')
          return
        }
      }

      // Desktop handling
      // Special handling for Phantom wallet
      if (connector.id === 'phantom') {
        const isSolanaMode = connector.virtualId === 'phantom-solana' || connector.isSolana

        if (isSolanaMode && window.phantom?.solana) {
          // Handle Solana connection
          try {
            const response = await window.phantom.solana.connect()
            const walletAddress = response.publicKey.toString()
            await checkExistingUser(walletAddress)
          } catch (error: any) {
            console.error('Phantom Solana connection error:', error)
            toast.error(error.message || 'Failed to connect Phantom Solana')
          }
          return
        }
      }

      // Default Ethereum connection for other wallets
      await connect({ connector })
    } catch (error: any) {
      console.error('Connection error:', error)
      toast.error(error.message || 'Failed to connect wallet')
    }
  }

  const getWalletsByCategory = (category: string) => {
    const wallets = connectors.filter(c => {
      if (category === 'Solana') {
        // Only show Phantom in Solana mode
        return c.id === 'phantom'
      }
      // For all other categories, use the standard wallet list (Phantom excluded from Ethereum)
      return walletCategories[category]?.includes(c.id)
    }).map(c => {
      // Add mode to Phantom wallet name (only for Solana)
      if (c.id === 'phantom') {
        return {
          ...c,
          name: 'Phantom (Solana)'
        }
      }
      return c
    })
    return wallets
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px] bg-gray-900 text-white">
        <DialogHeader>
          <div className="flex items-center justify-between">
            {showMore && (
              <Button
                variant="ghost"
                size="icon"
                className="w-8 h-8"
                onClick={() => setShowMore(false)}
              >
                <ArrowLeft className="h-4 w-4" />
              </Button>
            )}
            <DialogTitle className="text-2xl font-bold text-center flex-1">
              {showMore ? 'More Options' : 'Connect Wallet'}
            </DialogTitle>
            {showMore && <div className="w-8 h-8" />}
          </div>
        </DialogHeader>

        <div className="grid gap-4 py-4">
          {!showMore ? (
            <>
              {popularConnectors.map((connector) => (
                <Button
                  key={connector.virtualId || connector.id}
                  variant="outline"
                  className="w-full flex items-center justify-between px-4 py-6 hover:bg-gray-800"
                  disabled={isMobile ? isLoading || isAuthenticating : !connector.ready || isLoading || isAuthenticating}
                  onClick={() => handleConnect(connector)}
                >
                  <div className="flex items-center gap-3">
                    <Image
                      src={`/${connector.id.toLowerCase()}.svg`}
                      alt={`${connector.name} logo`}
                      width={32}
                      height={32}
                    />
                    <span className="text-lg">
                      {connector.name}
                      {(isLoading || isAuthenticating) && connector.id === pendingConnector?.id && 
                        ` (${isAuthenticating ? 'authenticating' : 'connecting'}...)`}
                    </span>
                  </div>
                  {isMobile && (connector.virtualId?.includes('phantom') || connector.id === 'phantom') && !hasPhantom ? (
                    <span className="text-sm text-blue-400">Install</span>
                  ) : isMobile && connector.id === 'metaMask' && !hasMetaMask ? (
                    <span className="text-sm text-blue-400">Install</span>
                  ) : connector.ready ? (
                    <ChevronRight className="h-5 w-5 text-gray-400" />
                  ) : (
                    <span className="text-sm text-red-400">Not installed</span>
                  )}
                </Button>
              ))}

              <Button
                variant="outline"
                className="w-full flex items-center justify-between px-4 py-6 hover:bg-gray-800"
                onClick={() => setShowMore(true)}
              >
                <span className="text-lg">More Options</span>
                <ChevronRight className="h-5 w-5 text-gray-400" />
              </Button>
            </>
          ) : (
            Object.entries(walletCategories).map(([category, walletIds]) => {
              const categoryWallets = connectors.filter(c => {
                if (c.id === 'phantom') {
                  // Only show Phantom in Solana category
                  return category === 'Solana'
                }
                return walletIds.includes(c.id)
              }).flatMap(c => {
                if (c.id === 'phantom') {
                  if (category === 'Solana') {
                    return [{
                      ...c,
                      virtualId: 'phantom-solana',
                      name: 'Phantom (Solana)',
                      isSolana: true
                    }]
                  }
                  return []
                }
                return [c]
              })

              if (categoryWallets.length === 0) return null

              return (
                <div key={category} className="space-y-2">
                  <h3 className="text-sm font-medium text-gray-400">{category}</h3>
                  {categoryWallets.map((connector) => (
                    <Button
                      key={connector.virtualId || connector.id}
                      variant="outline"
                      className="w-full flex items-center justify-between px-4 py-6 hover:bg-gray-800"
                      disabled={isMobile ? isLoading || isAuthenticating : !connector.ready || isLoading || isAuthenticating}
                      onClick={() => handleConnect(connector)}
                    >
                      <div className="flex items-center gap-3">
                        <Image
                          src={`/${connector.id.toLowerCase()}.svg`}
                          alt={`${connector.name} logo`}
                          width={32}
                          height={32}
                        />
                        <span className="text-lg">
                          {connector.name}
                          {(isLoading || isAuthenticating) && connector.id === pendingConnector?.id && 
                            ` (${isAuthenticating ? 'authenticating' : 'connecting'}...)`}
                        </span>
                      </div>
                      {isMobile && (connector.virtualId?.includes('phantom') || connector.id === 'phantom') && !hasPhantom ? (
                        <span className="text-sm text-blue-400">Install</span>
                      ) : isMobile && connector.id === 'metaMask' && !hasMetaMask ? (
                        <span className="text-sm text-blue-400">Install</span>
                      ) : connector.ready ? (
                        <ChevronRight className="h-5 w-5 text-gray-400" />
                      ) : (
                        <span className="text-sm text-red-400">Not installed</span>
                      )}
                    </Button>
                  ))}
                </div>
              )
            })
          )}
        </div>

        <p className="text-sm text-gray-400 text-center mt-4">
          By connecting your wallet, you agree to our{' '}
          <a href="#" className="text-blue-400 hover:text-blue-300">
            Terms of Service
          </a>{' '}
          and{' '}
          <a href="#" className="text-blue-400 hover:text-blue-300">
            Privacy Policy
          </a>
        </p>
      </DialogContent>
    </Dialog>
  )
} 