import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import connectDB from '@/lib/mongodb/mongoose'
import { User } from '@/models/User'

export async function GET() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.walletAddress) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    await connectDB()

    // Use exact match for Solana addresses, case-insensitive for others
    const walletQuery = session.user.walletType === 'solana'
      ? { walletAddress: session.user.walletAddress }
      : { walletAddress: { $regex: new RegExp(`^${session.user.walletAddress}$`, 'i') } }

    const user = await User.findOne(walletQuery)

    if (!user) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      )
    }

    return NextResponse.json(user)
  } catch (error) {
    console.error('Error fetching user:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
}

export async function PUT(req: Request) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.walletAddress) {
      return NextResponse.json(
        { message: 'Unauthorized' },
        { status: 401 }
      )
    }

    const data = await req.json()

    await connectDB()

    const user = await User.findOne({
      walletAddress: session.user.walletAddress.toLowerCase()
    })

    if (!user) {
      return NextResponse.json(
        { message: 'User not found' },
        { status: 404 }
      )
    }

    // Update allowed fields
    const allowedFields = ['username', 'biography', 'profileImage', 'bannerImage', 'links']
    allowedFields.forEach((field) => {
      if (data[field] !== undefined) {
        user[field] = data[field]
      }
    })

    await user.save()

    return NextResponse.json(
      { message: 'User updated successfully' },
      { status: 200 }
    )
  } catch (error) {
    console.error('Error updating user:', error)
    return NextResponse.json(
      { message: 'Internal server error' },
      { status: 500 }
    )
  }
} 