import { NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import connectDB from '@/lib/mongodb/mongoose'
import { User } from '@/models/User'
import { uploadMedia } from '@/lib/supabase'

// Supported image types and their extensions
const SUPPORTED_TYPES = {
  'image/jpeg': 'jpg',
  'image/png': 'png',
  'image/webp': 'webp',
  'image/gif': 'gif',
  'image/avif': 'avif',
  'image/svg+xml': 'svg',
  'video/webm': 'webm'
} as const

type SupportedMimeType = keyof typeof SUPPORTED_TYPES

export async function POST(req: Request) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user) {
      return new NextResponse('Unauthorized', { status: 401 })
    }

    const formData = await req.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as 'avatar' | 'banner'
    const profileType = formData.get('profileType') as 'coin' | 'personal'

    if (!file) {
      return new NextResponse('No file provided', { status: 400 })
    }

    if (!type || !['avatar', 'banner'].includes(type)) {
      return new NextResponse('Invalid file type', { status: 400 })
    }

    if (!profileType || !['coin', 'personal'].includes(profileType)) {
      return new NextResponse('Invalid profile type', { status: 400 })
    }

    // Validate file type
    if (!Object.keys(SUPPORTED_TYPES).includes(file.type)) {
      return new NextResponse(
        `Invalid file format. Supported formats: ${Object.keys(SUPPORTED_TYPES).join(', ')}`,
        { status: 400 }
      )
    }

    // Validate file size (max 8MB)
    const MAX_SIZE = 8 * 1024 * 1024 // 8MB
    if (file.size > MAX_SIZE) {
      return new NextResponse('File too large (max 8MB)', { status: 400 })
    }

    // Upload file to Supabase with user ID
    const { url, metadata } = await uploadMedia({
      userId: session.user.id,
      type,
      file,
      profileType
    })

    // Update user's profile with the new image URL
    await connectDB()
    const user = await User.findOne({ walletAddress: session.user.walletAddress })

    if (!user) {
      return new NextResponse('User not found', { status: 404 })
    }

    // Update both profiles with the new image and metadata
    const imageData = {
      url,
      isDefault: false,
      metadata
    }

    // Update the specified profile type
    if (type === 'avatar') {
      user[`${profileType}Profile`].avatar = imageData
    } else {
      user[`${profileType}Profile`].banner = imageData
    }

    await user.save()

    return NextResponse.json({ url, metadata })
  } catch (error: any) {
    console.error('Error uploading file:', error)
    return new NextResponse(error.message, { status: 500 })
  }
} 