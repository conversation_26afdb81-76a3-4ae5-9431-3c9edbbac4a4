'use client'

import { But<PERSON> } from '@/components/ui/button'
import { ExternalLink } from 'lucide-react'
import { ProfileType } from '@/models/User'
import { LINK_ICONS } from '@/lib/utils'
import { LinkIcon } from 'lucide-react'

// Define the same link arrays as in the dashboard
const COIN_PROFILE_LINKS = [
  { key: 'pumpfun', label: 'PumpFun' },
  { key: 'twitter', label: 'Twitter' },
  { key: 'telegram', label: 'Telegram' },
  { key: 'website', label: 'Website' },
  { key: 'chart', label: 'Chart' },
  { key: 'blockexplorer', label: 'Block Explorer' },
  { key: 'nftcollection', label: 'NFT Collection' },
  { key: 'staking', label: 'Staking' },
  { key: 'github', label: 'Github' },
  { key: 'farcaster', label: 'Farcaster' },
]

const PERSONAL_PROFILE_LINKS = [
  { key: 'twitter', label: 'Twitter' },
  { key: 'tradingbot', label: 'Trading Bot' },
  { key: 'telegram', label: 'Telegram' },
  { key: 'website', label: 'Website' },
  { key: 'github', label: 'Github' },
  { key: 'portfolio', label: 'Portfolio' },
  { key: 'instagram', label: 'Instagram' },
  { key: 'youtube', label: 'YouTube' },
  { key: 'facebook', label: 'Facebook' },
  { key: 'linkedin', label: 'LinkedIn' },
  { key: 'farcaster', label: 'Farcaster' },
]

interface ProfileContentProps {
  profile: ProfileType | null
  walletAddress?: string
  username?: string
}

export function ProfileContent({ profile, walletAddress, username }: ProfileContentProps) {
  if (!profile) return null

  // Determine which link array to use based on profile type
  const links = profile.type === 'coin' ? COIN_PROFILE_LINKS : PERSONAL_PROFILE_LINKS

  // Get custom links (links that aren't in the predefined arrays)
  const customLinks = Object.entries(profile.links || {})
    .filter(([key]) => !links.some(link => link.key === key))
    .map(([key]) => ({
      key,
      label: key.charAt(0).toUpperCase() + key.slice(1)
    }))

  // Combine predefined and custom links
  const allLinks = [...links, ...customLinks]

  return (
    <div className="space-y-8">
      {/* Biography */}
      {profile.biography && (
        <div className="mt-6">
          <p className="text-lg">{profile.biography}</p>
        </div>
      )}

      {/* Links */}
      <div className="space-y-4 pb-20">
        {allLinks.map(({ key, label }) => {
          const url = profile.links?.[key]
          if (!url) return null
          
          // Check if the link is a custom link (not in predefined arrays)
          const isCustomLink = !COIN_PROFILE_LINKS.some(link => link.key === key) && 
                             !PERSONAL_PROFILE_LINKS.some(link => link.key === key)
          
          // Use LinkIcon for custom links, otherwise use the predefined icon
          const Icon = isCustomLink ? LinkIcon : (LINK_ICONS[key.toLowerCase()] || ExternalLink)
          
          return (
            <div key={key} className="flex items-center gap-4 p-4 bg-card/70 backdrop-blur-sm rounded-lg">
              {Icon && <Icon className="w-5 h-5" />}
              <div className="flex-1">
                <span className="font-medium capitalize">
                  {label}
                </span>
              </div>
              <Button
                variant="ghost"
                size="icon"
                onClick={() => window.open(url.toString().startsWith('http') ? url.toString() : `https://${url}`, '_blank', 'noopener,noreferrer')}
              >
                <ExternalLink className="w-4 h-4" />
              </Button>
            </div>
          )
        })}
      </div>
    </div>
  )
} 