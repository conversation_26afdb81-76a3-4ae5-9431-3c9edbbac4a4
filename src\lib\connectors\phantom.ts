import { Connector, Chain } from 'wagmi'
import { InjectedConnector } from 'wagmi/connectors/injected'

export class PhantomConnector extends InjectedConnector {
  readonly id = 'phantom'
  readonly name = 'Phantom'
  readonly ready = typeof window !== 'undefined' && !!window.phantom?.ethereum

  constructor({ chains }: { chains?: Chain[] } = {}) {
    super({
      chains,
      options: {
        name: '<PERSON>',
        shimDisconnect: true,
        getProvider: () => {
          if (typeof window === 'undefined') return undefined
          const provider = window.phantom?.ethereum
          if (!provider) return undefined
          
          // Ensure the provider is in Ethereum mode
          if (typeof provider.setEthereumMode === 'function') {
            provider.setEthereumMode()
          }
          
          return provider
        },
      },
    })
  }

  async connect({ chainId }: { chainId?: number } = {}) {
    try {
      const provider = await this.getProvider()
      if (!provider) throw new Error('Please install Phantom wallet')
      
      // Ensure we're in Ethereum mode if the method exists
      if (typeof provider.setEthereumMode === 'function') {
        await provider.setEthereumMode()
      }
      
      const accounts = await provider.request({
        method: 'eth_requestAccounts',
      })
      
      if (!accounts || accounts.length === 0) {
        throw new Error('No accounts found')
      }
      
      const chain = await this.getChainId()
      
      return {
        account: accounts[0] as string,
        chain: { id: chain, unsupported: false },
      }
    } catch (error: any) {
      // Improve error messages for users
      if (error.code === 4001) {
        throw new Error('Please approve the connection request in your Phantom wallet')
      }
      if (!window.phantom?.ethereum) {
        throw new Error('Please install Phantom wallet')
      }
      throw error
    }
  }
} 